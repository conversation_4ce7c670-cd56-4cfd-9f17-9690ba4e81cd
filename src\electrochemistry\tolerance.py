from copy import deepcopy
from math import fabs
from itertools import product

from utils.mapped_attributes import set_mapped_attribute, get_mapped_attribute
from models.cell_design_model import ToleranceMetricsVariableModel, TolearanceMetricsModel, CellDesignModel, ToleranceCellDesign
from models.tolerance_result_model import ToleranceResponseModel, ToleranceResultModel
from electrochemistry.factory import FullCellBuilder

variable_request_map = {
    "anodeQAim":"anode_q_aim",
    "anodeQAimFirstCharge":"anode_q_aim_first_charge",
    "cathodeQAim":"cathode_q_aim",
    "cathodeQAimFirstCharge":"cathode_q_aim_first_charge",
    "anodeLoading":"anode_loading",
    "cathodeLoading":"cathode_loading",
    "npRatioRev":"np_ratio_rev",
    "cathodeCoating": "cathode_coating",
    "anodeCoating": "anode_coating",
    "thicknessSeparator": "thickness_separator",
}

def variable_mapper(name):
    return variable_request_map.get(name, name)

tolerance_result_map = [
    {"id":"np_ratio","field": "balancing.get_np_ratio_rev()", "name": "N/P Ratio Reversible", "unit":None},
    {"id":"cell_layer_capacity","field": "cell_layer.capacity_c10", "name": "Capacity per cell layer C/10 [Ah]", "unit":"Ah"},
    {"id":"anode_areal_capacity","field": "cell_layer.anode.areal_capacity", "name": "Anode areal capacity [mAh/cm²]", "unit":"mAh/cm²"},
    {"id":"cathode_areal_capacity","field": "cell_layer.cathode.areal_capacity", "name": "Cathode areal capacity [mAh/cm²]", "unit":"mAh/cm²"},
    {"id":"full_cell_capacity","field": "safe_capacity", "name": "Cell capacity [Ah]", "unit":"Ah"},
    {"id":"cf3_breathing_with_compression","field": "cf3_breathing_with_compression", "name": "CF3 Breathing with compression [%]", "unit":"%"},
    {"id":"weight","field": "weight", "name": "Weight [g]", "unit":"g"}
]

class Tolerance:

    def __init__(self, design:ToleranceCellDesign):
        self._original_design:ToleranceCellDesign = design
        self._settings:TolearanceMetricsModel = self._original_design.tolerance_settings
        self._standard_full_cell = FullCellBuilder(self._original_design) \
                                        .calculate_tolerance_variables() \
                                        .build()
        self._all_cells = []

    def get_tolerance_response(self):
        result = ToleranceResponseModel()

        for variable in self._settings.variables:
            result.variable_results[variable.id] = self._tolerance_results(variable)
        result.chained_result = self._chained_result()

        return result
    
    def _chained_result(self):
        self._make_design_combinations()
        
        result = []
        for tr in tolerance_result_map:
            result.append(self._get_min_max(tr))
        
        return result
    
    def _make_design_combinations(self):
        limits_list = []
        for variable in self._settings.variables:
            (lower_limit, higher_limit) = self._calculate_limits(variable)
            limits_list.append([
                (higher_limit, variable.id),
                (get_mapped_attribute(self._original_design, variable_mapper(variable.id)), variable.id),
                (lower_limit, variable.id)
            ])

        all_combinations = list(product(*limits_list)) 

        for combination in all_combinations:
            new_design = deepcopy(self._original_design)
            for value, var in combination:
                set_mapped_attribute(new_design, variable_mapper(var), value)
            full_cell = self._make_cell(new_design)
            if full_cell is not None:
                self._all_cells.append(full_cell)
    
    def _tolerance_results(self, variable):
        return [self._tolerance_result_model(tr, variable) for tr in tolerance_result_map]
    
    def _tolerance_result_model(self, tolerance_result, variable):
        (lower_cell,standard_cell, higher_cell) = self._cells(variable) 

        return ToleranceResultModel(
            tolerance_result["id"],
            tolerance_result["name"],
            get_mapped_attribute(lower_cell, tolerance_result["field"]),
            get_mapped_attribute(standard_cell, tolerance_result["field"]),
            get_mapped_attribute(higher_cell, tolerance_result["field"]),
            tolerance_result["unit"]
        )
    
    def _cells(self, variable):
        (lower_design, higher_design) = self._designs(variable)
        return [self._make_cell(lower_design), self._make_cell(self._original_design), self._make_cell(higher_design)]

    def _designs(self, variable):
        (lower_limit, higher_limit) = self._calculate_limits(variable)
        higher_design = deepcopy(self._original_design)
        set_mapped_attribute(higher_design, variable_mapper(variable.id), higher_limit)
        lower_design = deepcopy(self._original_design)
        set_mapped_attribute(lower_design, variable_mapper(variable.id), lower_limit)
        return (lower_design, higher_design,)

    def _make_cell(self, design):
        try:
            return FullCellBuilder(design) \
                .set_no_cell_layers(self._standard_full_cell.cell_format.no_cell_layers) \
                .build()
        except ZeroDivisionError:
            return None

    def _get_min_max(self, tolerance_result):
        minimum = min(self._all_cells, key= lambda x: self._value_or_zero(x, tolerance_result["field"]))
        maximum = max(self._all_cells, key= lambda x: self._value_or_zero(x, tolerance_result["field"]))
        return ToleranceResultModel(
            tolerance_result["id"],
            tolerance_result["name"],
            get_mapped_attribute(minimum, tolerance_result["field"]),
            get_mapped_attribute(self._standard_full_cell, tolerance_result["field"]),
            get_mapped_attribute(maximum, tolerance_result["field"]),
            tolerance_result["unit"],
            [get_mapped_attribute(cell, tolerance_result["field"]) for cell in self._all_cells]
        )

    def _value_or_zero(self, x, field_name):
        value = get_mapped_attribute(x, field_name)
        if value is None:
            return 0
        return value
    
    def _calculate_limits(self, variable:ToleranceMetricsVariableModel):
        standard_value = get_mapped_attribute(self._original_design, variable_mapper(variable.id))
        match self._settings.value_type:
            case "relative":
                calculated_lower_limit = standard_value - fabs(variable.lower_limit)
                calculated_higher_limit = standard_value + fabs(variable.upper_limit)
            case "absolute":
                calculated_lower_limit = variable.lower_limit
                calculated_higher_limit = variable.upper_limit
            case "relativePercentage":
                calculated_lower_limit = standard_value * (1 - fabs(variable.lower_limit)/100) 
                calculated_higher_limit = standard_value * (1 + fabs(variable.upper_limit)/100) 
        return (calculated_lower_limit, calculated_higher_limit)
