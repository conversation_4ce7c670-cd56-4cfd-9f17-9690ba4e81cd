import unittest
from cell_design_export_blueprint import create_chapters
from electrochemistry.factory import create_full_cell
from models.cell_design_model import CellDesignModel, MaterialWeightModel
from cell_design_metrics_blueprint import calculate

class TestCreatingElectrochemistryModel(unittest.TestCase):

    def test_default_values(self):
        design = CellDesignModel(
            cathode_materials=[MaterialWeightModel(material_id=1001, weight_percent=100)],            
            cathode_q_aim=None,
            anode_materials=[MaterialWeightModel(material_id=4, weight_percent=70), MaterialWeightModel(material_id=2, weight_percent=30)],
            anode_q_aim=None,
            prelithiation_capacity=0.0,
            balancing_np_ratio_first=1.1,
            balancing_u_max=4.24,
            balancing_u_min=2.5,
            active_material_anode_weight_percent = 94.7,
            active_material_cathode_weight_percent = 96.0,
            binder_material_anode_weight_percent = 4.0,
            binder_material_cathode_weight_percent = 2.0,
            leitruss_anode_weight_percent = 1.25,
            leitruss_cathode_weight_percent = 2.0,
            cnt_anode_weight_percent = 0.05,
            cnt_cathode_weight_percent = 0.0,
            prelithiation_process='nanoscale',
            cathode_calander_density = 3.5,
            anode_calander_density = 1.0,
            cathode_areal_capacity = 4.0,
            thickness_aluminium_foil = 12.0,
            thickness_copper_foil = 8.0,
            thickness_separator = 18.0,
            cell_format = 'CF1_thin',
            cell_format_properties = {
                'isDefault': True,
                'hasTwoSubstacks': False,
                'cellLength': 354.0,
                'cellWidth': 102.0,
                'cellThickness': 10.72,
                'housingWeight': 17.0,
                'coatingLengthCathode': 316.0,
                'coatingWidthCathode': 94.0,
                'coatingLengthAnode': 318.0,
                'coatingWidthAnode': 95.0,
                'coatingLengthSeparator': 322.0,
                'coatingWidthSeparator': 97.0,
                'housingThickness': 0.183,
                'swellingBuffer': 0.0
            },
            electrolyte_swelling = 0.03,
            electrolyt_amount = 1.4,
            safety = 0.95,
            parallel_cells_count = 2,
            serial_cells_count = 198,
            module_count = 1,
            scrap = 0.01
            
        )

        full_cell = create_full_cell(design)
        chapters = create_chapters()
        # print(full_cell.cell_layer.cathode.binder_material.name)
        self.assertEqual(chapters[0]['columns'][0]['fn'](full_cell), '1.0 : BASF NCM83 Polycrystalline')
        # self.assertAlmostEqual(chapters[0]['columns'][1]['fn'](full_cell), 'BTR Graphit')
        # self.assertAlmostEqual(chapters[0]['columns'][2]['fn'](full_cell), 0)        
        # self.assertAlmostEqual(chapters[1]['columns'][0]['fn'](full_cell), )        
        # self.assertAlmostEqual(chapters[1]['columns'][1]['fn'](full_cell), )        
        # self.assertAlmostEqual(chapters[1]['columns'][2]['fn'](full_cell), )
        # self.assertAlmostEqual(chapters[1]['columns'][3]['fn'](full_cell), )        
        # self.assertAlmostEqual(chapters[1]['columns'][4]['fn'](full_cell), )
        # self.assertAlmostEqual(chapters[1]['columns'][5]['fn'](full_cell), )
        
        
