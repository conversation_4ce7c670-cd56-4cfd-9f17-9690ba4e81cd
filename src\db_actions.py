from bson import ObjectId
import datetime
from typing import Any
import jsons
from pymongo import ReturnDocument

from database import database
from models.user_model import UserModel
from utils.camelcase_to_snakecase import camelcase_to_snakecase

def update_user_table(claims: dict[str, Any]) -> UserModel:
    document = database['users'].find_one_and_update(
        { 'oid': claims['oid'] },
        { '$set': { 'oid': claims['oid'], 'email': claims['upn'], 'name': claims['name'] } },
        upsert = True,
        return_document = ReturnDocument.AFTER
    )

    return jsons.load(document, UserModel, key_transformer = camelcase_to_snakecase)

def delete_optimization(optimization_id: ObjectId, user: UserModel) -> None:
    at = datetime.datetime.now(tz=datetime.timezone.utc)
    metadata = {
        'deletedAt': at,
        'deletedBy': user._id
    }
    database['optimizations'].find_one_and_update({ '_id': optimization_id }, { '$set': metadata })
