from dataclasses import dataclass
import json
import os
from typing import Any, Optional

import jsons
from electrochemistry.active_material import ActiveMaterial, ActiveMaterialType
from electrochemistry.blending import FormationWeight, calculate_blend_formations
from electrochemistry.formation_table import FormationTable
from electrochemistry.material import Material, MaterialType, MaterialWeight
from utils.camelcase_to_snakecase import camelcase_to_snakecase
from jsons_config import jsons_mongodb_fork

def _load_json_file(file: str) -> Any:
    with open(file, "rb") as f:
        return json.load(f)

@dataclass
class BlendCompositionDefinition:
    material_id: str
    weight_percent: float

@dataclass
class MaterialActivityDefinition:
    formations: Optional[dict] = None
    formations_history: Optional[dict] = None
    composition: Optional[list[BlendCompositionDefinition]] = None

@dataclass
class MaterialDefinition:
    id: str
    elektra_id: Optional[str]
    name: str
    type: MaterialType
    is_anode: bool
    is_cathode: bool
    density: Optional[float]
    sei_loss_ah: Optional[float]
    active_surface: Optional[float]
    activity: Optional[MaterialActivityDefinition] = None

class MaterialCollection:

    material_definitions: dict[str, MaterialDefinition]
    materials: dict[str, Material]

    def __init__(self, path: str):
        files = [os.path.join(fdirpath, filename) for (fdirpath, dirnames, filenames) in os.walk(path) for filename in filenames]
        definitions = [jsons.load(_load_json_file(file), MaterialDefinition, fork_inst = jsons_mongodb_fork, key_transformer = camelcase_to_snakecase, transform_dict_keys = False) for file in files]

        self.material_definitions = { definition.id: definition for definition in definitions }
        self.materials = { material.id: material for material in [self._build_material(definition) for definition in self.material_definitions.values()] }

    def get_materials(self) -> list[Material]:
        return [material for material in self.materials.values()]

    def get_inactive_materials(self) -> list[Material]:
        return [material for material in self.materials.values() if material.type != MaterialType.ACTIVE_MATERIAL]

    def get_active_materials(self) -> list[ActiveMaterial]:
        return [material for material in self.materials.values() if isinstance(material, ActiveMaterial)]

    def get_material(self, id: str) -> Material:
        definition = self.material_definitions.get(id)
        return self._build_material(definition)

    def get_active_material(self, id: str, history: Optional[str] = None) -> ActiveMaterial:
        material = self.get_material(id)
        if not isinstance(material, ActiveMaterial):
            raise 'material is not active'
        if history is not None:
            material = material.get_history_material(history)

        return material

    def _build_material(self, definition: MaterialDefinition) -> Material:
        active_surface = self._get_active_surface(definition, 1.0)
        if definition.activity is not None:

            if "dates" in definition.activity.formations:
                formations_history = self._get_activity_formation_history(definition.activity)
                # formations: need this only for old format, will be removed in future (?)
                formations = formations_history[min(formations_history)]
                composition = self._get_composition(definition.activity, 1.0)

            else:
                formations_history = None 
                formations = self._get_activity_formations(definition.activity)
                composition = self._get_composition(definition.activity, 1.0)

            return ActiveMaterial(
                id = definition.id,
                name = definition.name,
                density = definition.density,
                type = definition.type,
                active_surface = active_surface,
                active_material_type = ActiveMaterialType.ANODE if definition.is_anode else ActiveMaterialType.CATHODE,
                formations = formations,
                formations_history = formations_history,
                composition = composition
            )
        else:
            return Material(
                id = definition.id,
                name = definition.name,
                density = definition.density,
                sei_loss_ah = definition.sei_loss_ah,
                type = definition.type,
                active_surface = active_surface,
                is_anode = definition.is_anode,
                is_cathode = definition.is_cathode
            )
        
    def _get_formations(self, material_id: str) -> FormationTable:
        definition = self.material_definitions.get(material_id)
        if definition.activity is not None:
            raise 'material is not active'
        
        return self._get_activity_formations(definition.activity)

    def _get_activity_formation_history(self, activity) -> dict:
        #dict of Dates (keys) and FormationTable (values)
        measurement_list_FormationTable = [self._get_activity_formationtable_history(activity.composition, activity_i) for activity_i in activity.formations['measurement']]
        return dict(zip(activity.formations['dates'], measurement_list_FormationTable))
    
    def _get_activity_formationtable_history(self, composition, formations: MaterialActivityDefinition) -> FormationTable:    
        #changed _get_activity_formations() methode         
        if formations is not None:
            return FormationTable.from_dict(formations)
        elif composition is not None:
            calculate_blend_formations([FormationWeight(formations = self._get_formations(component.material_id), weight = component.weight_percent) for component in composition])
        else:
            raise 'ActiveMaterial formations are not constructible'


    def _get_activity_formations(self, activity: MaterialActivityDefinition) -> FormationTable:            
        if activity.formations is not None:
            return FormationTable.from_dict(activity.formations)
        elif activity.composition is not None:
            calculate_blend_formations([FormationWeight(formations = self._get_formations(component.material_id), weight = component.weight_percent) for component in activity.composition])
        else:
            raise 'ActiveMaterial formations are not constructible'
        
    def _get_composition(self, activity: MaterialActivityDefinition, factor: float) -> Optional[list[MaterialWeight]]:
        if activity.composition is not None:
            definitions = [(self.material_definitions[component.material_id], component.weight_percent * factor) for component in activity.composition]
            materials = [self._get_composition(definition.activity, weight_percent) if (definition.activity is not None and definition.activity.composition is not None) else [MaterialWeight(self._build_material(definition), weight_percent)] for (definition, weight_percent) in definitions]
            return [material for inner in materials if inner is not None for material in inner]
        else:
            return None
        
    def _get_active_surface(self, definition: MaterialDefinition, factor: float) -> Optional[float]:
        if definition.active_surface is None:
            if definition.activity is not None and definition.activity.composition is not None:
                active_surfaces = [self._get_active_surface(self.material_definitions.get(component.material_id), component.weight_percent) for component in definition.activity.composition]
                weight_percents = [component.weight_percent for component in definition.activity.composition]
                if any(active_surface is None for active_surface in active_surfaces):
                    result = None
                else:
                    result = sum(active_surfaces) / sum(weight_percents)
            else:
                result = None
        else:
            result = definition.active_surface

        return result * factor if result is not None else None

material_collection: MaterialCollection = MaterialCollection(os.path.join(os.getcwd(), 'data', 'materials'))
