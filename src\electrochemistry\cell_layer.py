
from dataclasses import dataclass
from electrochemistry.component import Component
from electrochemistry.electrode import Electrode

@dataclass
class CellLayer(object):
    cathode: Electrode
    anode: Electrode
    al_cc: Component
    cu_cc: Component
    separator: Component

    def get_thickness(self) -> float:
        return self.cu_cc.thickness / 2 + self.al_cc.thickness / 2 + self.anode.coating_thickness + self.cathode.coating_thickness + self.separator.thickness
