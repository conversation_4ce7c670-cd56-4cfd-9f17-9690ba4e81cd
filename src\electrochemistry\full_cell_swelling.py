from __future__ import annotations
from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from electrochemistry.full_cell import FullCell
    from electrochemistry.material import MaterialWeight

from electrochemistry.cell_format import CellFormatPouch
from electrochemistry.constants import SWELLING_CONSTANT_A, SWELLING_CONSTANT_B
from models.cell_design_metrics_model import CellDesignMetricsSwellingModel
from copy import copy

ALLOWED_MATERIAL_ID = "group14_scc55"
ALLOWED_FORMATION_ID = "cf3"

class FullCellSwelling:
    _default_model = CellDesignMetricsSwellingModel(
            swelling_constant_a=SWELLING_CONSTANT_A,
            swelling_constant_b=SWELLING_CONSTANT_B,
            total_breathing_per_layer = 0,
            stack_breathing = 0,
            cf3_uncompressed_breathing = 0,
            cf3_breathing_with_compression = 0,
            cf3_absolut_breathing_with_compression = 0,
            free_space_after_formation = 0
        )

    def __init__(self, full_cell:FullCell):
        self._full_cell = full_cell

        self.total_breathing_per_layer = None
        self.stack_breathing = None
        self.cf3_uncompressed_breathing = None
        self.cf3_breathing_with_compression = None
        self.cf3_absolut_breathing_with_compression = None
        self.free_space_after_formation = None

        self.calculate_swelling()

    def calculate_swelling(self):
        if self.can_calculate():
            self.total_breathing_per_layer = self._get_layer_swelling()
            self.stack_breathing = self._get_stack_swelling() * 100
            self.cf3_uncompressed_breathing = self._get_formation_swelling() * 100
            self.cf3_breathing_with_compression = self._get_formation_compressed_swelling() * 100
            self.cf3_absolut_breathing_with_compression = self._get_formation_compressed_swelling() * self._full_cell.cell_format.properties.cell_thickness
            self.free_space_after_formation = self._get_free_space_after_foramtion()


    def get_swelling_model(self) -> CellDesignMetricsSwellingModel:
        if not self.can_calculate():
            return self._default_model

        model = copy(self._default_model)
        model.total_breathing_per_layer = self.total_breathing_per_layer
        model.stack_breathing = self.stack_breathing
        model.cf3_uncompressed_breathing = self.cf3_uncompressed_breathing
        model.cf3_breathing_with_compression = self.cf3_breathing_with_compression
        model.cf3_absolut_breathing_with_compression = self.cf3_absolut_breathing_with_compression
        model.free_space_after_formation = self.free_space_after_formation
        
        return model
    
    def can_calculate(self):
        if not hasattr(self._full_cell, "swelling_during_formation") or self._full_cell.swelling_during_formation is None:
            return False
        
        if not hasattr(self._full_cell, "compressibility_over_pressure_range") or self._full_cell.compressibility_over_pressure_range is None:
            return False

        if self._get_material_weight() is None:
            return False
        
        if ALLOWED_FORMATION_ID.lower() != self._full_cell.cell_format.id.lower():
            return False
        
        if not isinstance(self._full_cell.cell_format, CellFormatPouch):
            return False
        
        return True
    
    def _get_free_space_after_foramtion(self) -> float:
        layer_count = self._full_cell.cell_format.no_cell_layers
        layer_free_space = self._full_cell.cell_format.delta_cell_layer_thickness
        free_space_after_formation = (layer_free_space - layer_count * self._full_cell.swelling_during_formation * self._get_thickness_after_formation()) + (self._full_cell.cell_format.properties.swelling_buffer * 1000)
        return free_space_after_formation if free_space_after_formation > 0 else 0

    def _get_stack_swelling(self)->float:
        layer_swelling = self._get_layer_swelling()
        return layer_swelling/self._get_thickness_after_formation()
    
    def _get_thickness_after_formation(self) -> float:
        return self._full_cell.cell_layer.get_thickness()
    
    def _get_formation_compressed_swelling(self) -> float:
        cf3_swelling = self._get_formation_swelling()
        return cf3_swelling - self._full_cell.compressibility_over_pressure_range

    def _get_formation_swelling(self)->float:
        layer_swelling = self._get_layer_swelling()
        cf3_thickness = self._full_cell.cell_format.properties.cell_thickness
        layer_count = self._full_cell.cell_format.no_cell_layers
        return (layer_swelling * layer_count - self._get_free_space_after_foramtion())*1e-3/cf3_thickness

    def _get_layer_swelling(self) -> float:
        return self._get_material_volume_ratio() * SWELLING_CONSTANT_A * pow(self._max_sol_anode(), SWELLING_CONSTANT_B) * self._full_cell.cell_layer.anode.coating_thickness

    def _get_material_volume_ratio(self):
        return self._get_material_weight_ratio() / self._get_material_weight().material.density * self._full_cell.cell_layer.anode.density

    def _get_material_weight_ratio(self) -> float:
        return self._get_material_weight().weight * self._full_cell.cell_layer.anode.get_active_material_weight_without_lithium()
    
    def _max_sol_anode(self):
        return 1/self._full_cell.balancing.get_np_ratio_rev()
    
    def _get_material_weight(self) -> MaterialWeight:
        try:
            return next(filter(lambda mw: mw.material.id == ALLOWED_MATERIAL_ID, self._full_cell.cell_layer.anode.active_material.materials))
        except StopIteration:
            return None 
    