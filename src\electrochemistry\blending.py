from dataclasses import dataclass
import math
from typing import Optional, Tuple
import pandas as pd
from electrochemistry.formation_table import Formation, FormationTable
from utils.interpolate import interpolate

@dataclass
class FormationWeight:
    formations: FormationTable
    weight: float

def calculate_blend_formations(formation_weights: list[FormationWeight]) -> FormationTable:
    if len(formation_weights) == 0:
        return None
    elif len(formation_weights) == 1:
        return formation_weights[0].formations
    elif len(formation_weights) == 2:

        formation_descriptions = [
            'Formierung 1. Laden',
            'Formierung 1. Entladen',
            'Formierung 2. Laden',
            'C/10 Entladen', # swapped
            'C/10 Laden', # swapped
            'C/3 Entladen',
            'C/2 Entladen',
            '1C Entladen'
        ]

        result_formations: list[Formation] = list()

        ## Step 1: Create Monotonous Formation Cycles
        formation_tables = [entry.formations.monotonize_all() for entry in formation_weights]
        weights = [entry.weight for entry in formation_weights]

        ## Step 2 Create Formation Cycles
        ## Step 2: Blend Cycles
        q_ends: Optional[list[float]] = [m.formations.get_formation_first_charge().q_min for m in formation_weights]

        for description in formation_descriptions:
            formations = [formation_table.get_formation(description) for formation_table in formation_tables]
            formations = [formation.redistribute_q() for formation in formations]
            if q_ends is None or any(f is None or f.cycle is None for f in formations):
                result_formations.append(Formation(description, None))
                q_ends = None
            else:
                (u_start, u_end) = get_u_range(q_ends, formations, weights)
                u_samples = get_u_samples(u_start, u_end, 1000)
                result = Formation(description, blend_single_formation(q_ends, u_samples, formations, weights))
                result_formations.append(result)

                ## e) Update End Capacity and switch to next half-cycle
                if description in ['Formierung 1. Laden', 'Formierung 1. Entladen', 'Formierung 2. Laden']:
                    q_ends = [interpolate([u_end], formation.cycle['SpannungV'], formation.cycle['LadungMAhg'])[0] for formation in formations]
                elif description == 'C/10 Entladen' and all(map(lambda formation_table: formation_table.get_formation_c_10_charge() is not None, formation_tables)): # preparation for C/10 Charge
                    u_end = result.cycle['SpannungV'].min() if result.is_u_decreasing() else result.cycle['SpannungV'].max()
                    q_ends = [interpolate([u_end], formation_table.get_formation_c_10_discharge().cycle['SpannungV'], formation_table.get_formation_c_10_discharge().cycle['LadungMAhg'])[0] for formation_table in formation_tables]
                else: # Rate Tests
                    q_ends = [formation_table.get_formation_second_charge().cycle['LadungMAhg'].iat[-1] for formation_table in formation_tables]
        
        return FormationTable(result_formations)

def get_u_range(q_ends: list[float], formations: list[Formation], weights: list[float]) -> Tuple[float, float]:
    ## b) Get voltage limits
    ## b1) Get start voltage for previous end capacity
    is_u_decreasing = sum([formation.get_u_mean_diff() * weight for (formation, weight) in zip(formations, weights)]) < 0

    u_starts = [interpolate([q_end], formation.cycle['LadungMAhg'], formation.cycle['SpannungV'])[0] for (formation, q_end) in zip(formations, q_ends)]

    u_start = max(u_starts) if is_u_decreasing else min(u_starts)
    if u_start is None or math.isnan(u_start):
        u_start = min([formation.cycle['SpannungV'].max() for formation in formations]) if is_u_decreasing else max([formation.cycle['SpannungV'].min() for formation in formations])

    ## b2) Get end voltage
    u_end = max([formation.cycle['SpannungV'].min() for formation in formations]) if is_u_decreasing else min([formation.cycle['SpannungV'].max() for formation in formations])
    return (u_start, u_end)

def get_u_samples(u_start: float, u_end: float, samples: int) -> pd.Series:
    return pd.Series(range(0, samples)) * ((u_end - u_start) / (samples - 1)) + u_start

def blend_single_formation(q_ends: list[float], u_samples: pd.Series, formations: list[Formation], weights: list[float]) -> pd.DataFrame:
    if any(map(lambda f: f is None or f.cycle is None, formations)):
        return None
    else:
        is_q_decreasing = sum([formation.get_q_mean_diff() * weight for (formation, weight) in zip(formations, weights)]) < 0
        if is_q_decreasing:
            q_indices = [formation.cycle['LadungMAhg'] <= q_end for (formation, q_end) in zip(formations, q_ends)]
        else:
            q_indices = [formation.cycle['LadungMAhg'] >= q_end for (formation, q_end) in zip(formations, q_ends)]
        formations = [Formation(formation.description, formation.cycle.loc[q_index]) for formation, q_index in zip(formations, q_indices)]
        formations = [formation.redistribute_u(len(u_samples)) for formation in formations]
        ## c) Blend Capacities with weight percents
        ## First only the component lower in voltage is active and the other
        ## component remains at its starting charge. This reflects in the
        ## Qiend, which is the extrapolation value for this case
        cur_q_tot = sum([weight * pd.Series(interpolate(u_samples, formation.cycle['SpannungV'], formation.cycle['LadungMAhg'])) for (weight, formation) in zip(weights, formations)])

        ## d) Assign Table
        return pd.DataFrame({
            'LadungMAhg': cur_q_tot,
            'SpannungV': u_samples
        })
