from auth import bearer
from flask import Blueprint
from electrochemistry.active_material import ActiveMaterial, ActiveMaterialType

from electrochemistry.material_collection import material_collection

active_material_blueprint = Blueprint('material_api', __name__, url_prefix='/api/material')

active_materials = material_collection.get_active_materials()

def _active_material_to_dict(active_material: ActiveMaterial) -> dict:
    match active_material.active_material_type:
        case ActiveMaterialType.CATHODE:
            type = 'cathode'
        case ActiveMaterialType.ANODE:
            type = 'anode'

    return { 'id': active_material.id, 'name': active_material.name, 'type': type, 'blendable': len(active_material.composition) == 1, 'history': [*active_material.formations_history.keys()] if active_material.formations_history is not None else []}

@active_material_blueprint.get('/')
@bearer.secure('user')
def get_all_materials():
    return [_active_material_to_dict(m) for m in active_materials]

@active_material_blueprint.get('/<string:material_id>')
@bearer.secure('user')
def get_single_material(material_id: str):
    return next(_active_material_to_dict(m) for m in active_materials if m.id == material_id)
