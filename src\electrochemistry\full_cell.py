import os
from typing import Optional
import numpy as np
import pandas as pd
from electrochemistry.balancing import Balancing
from electrochemistry.cell_format import CellFormat
from electrochemistry.cell_layer import CellLayer
from electrochemistry.solid_electrolyte_interface import SolidElectrolyteInterface
from models.cell_design_model import CellDesignModel
from electrochemistry.electrolyte import Electrolyte
from electrochemistry.full_cell_swelling import FullCellSwelling
from electrochemistry.material import Material
from electrochemistry.material import MaterialType
from models.table_model import TableCellEditModel, TableCellModel, TableModel, TableRowModel
from utils.exception_to_none import exception_to_none


# This is recoded from fullcell.m file - properties and their methods. Beside this file, we added cell_design_model.py, cell_format.py and cell_layer.py

class FullCell(object):
    prices: pd.DataFrame
    business_case: str
    custom_prices: dict[str, float]

    cell_format: CellFormat
    cell_layer: CellLayer
    balancing: Balancing
    cathode_weight: float
    anode_weight: float
    bill_of_materials: pd.DataFrame
    no_of_cell_layers: int
    electrolyte: Electrolyte
    tabs: Material
    weight: float
    volume: float
    pore_volume: float
    pore_volume_ah: float   
    energy_density_kg_c10: float
    energy_density_l_c10: float
    energy_density_kg_c3: float
    energy_density_l_c3: float
    capacity: float
    capacity_c10: float
    capacity_c3: float
    u_nom: float
    u_nom_c10: float
    u_nom_c3: float
    energy_c10: float
    energy_c3: float
    energy: float
    materials: list[Material]

    price: Optional[float]
    cathode_price: Optional[float]
    anode_price: Optional[float]

    price_kg: Optional[float]
    cathode_price_kg: Optional[float]
    anode_price_kg: Optional[float]

    price_kwh_c10: Optional[float]
    cathode_price_c10: Optional[float]
    anode_price_c10: Optional[float]

    price_kwh_c3: Optional[float]
    cathode_price_c3: Optional[float]
    anode_price_c3: Optional[float]

    safety: float
    safe_capacity_c10: float
    safe_energy_c10: float
    safe_energy_density_l: float
    safe_energy_density_kg: float
    safe_energy_density_l_c10: float
    safe_energy_density_kg_c10: float
    safe_capacity_c3: float
    safe_energy_c3: float
    safe_capacity: float
    safe_energy: float
    safe_energy_density_l_c3: float
    safe_energy_density_kg_c3: float
    formation_charges: float
    scrap: float
    resistances: float
    sei: SolidElectrolyteInterface
    thermal_table: float
    thermal_mass: float
    resistance: float
    resistance_rel: float
    separator_porosity: float
    swelling_during_formation: float
    compressibility_over_pressure_range: float
    swelling: FullCellSwelling
   

    # many more to come
    def __init__(self, app: CellDesignModel, cell_layer: CellLayer, balancing: Balancing, cell_format: CellFormat, electrolyte: Electrolyte):
        self.prices = self.load_excel_prices()
        self.business_case = app.business_case_id
        self.custom_prices = app.bom_prices
        if app.swelling_during_formation is not None:
            self.swelling_during_formation = app.swelling_during_formation / 100
        if app.compressibility_over_pressure_range is not None:
            self.compressibility_over_pressure_range = app.compressibility_over_pressure_range / 100

        self.cell_format = cell_format
        self.safety = app.safety
        self.volume = cell_format.volume / 1e6
        self.cell_layer = cell_layer
        self.balancing = balancing
        self.u_nom_c10 = self.balancing.cell_formations.get_formation_c_10_discharge().u_nom
        self.u_nom_c3 = self.balancing.cell_formations.get_formation_c_3_discharge().u_nom
        self.capacity_c10 = self.balancing.cell_formations.get_formation_c_10_discharge().capacity / 1000 * self.cell_layer.cathode.weight * self.cell_layer.cathode.active_material_weight * self.cell_format.no_cell_layers
        self.capacity_c3 = exception_to_none(lambda: self.balancing.cell_formations.get_formation_c_3_discharge().capacity / 1000 * self.cell_layer.cathode.weight * self.cell_layer.cathode.active_material_weight * self.cell_format.no_cell_layers)
        self.energy_c10 = self.capacity_c10 * self.u_nom_c10
        self.energy_c3 = exception_to_none(lambda: self.capacity_c3 * self.u_nom_c3)

        if self.energy_c3 is None or np.isnan(self.energy_c3):
            self.energy = self.energy_c10
            self.u_nom = self.u_nom_c10
            self.capacity = self.capacity_c10
        else:
            self.energy = self.energy_c3
            self.u_nom = self.u_nom_c3
            self.capacity = self.capacity_c3

        self.electrolyte = electrolyte
        self.electrolyte = Electrolyte(electrolyte.material, electrolyte.swelling, electrolyte.amount, self.capacity)

        self.tabs = Material(id = "tab", name = "Tab", density = 0, type = MaterialType.TAB, theoretical_capacity = 0)
        self.scrap = app.scrap / 100

        self.cathode_weight = 2 * self.cell_layer.cathode.weight * self.cell_format.no_cathode_layers
        self.anode_weight = 2 * self.cell_layer.anode.weight * self.cell_format.no_anode_layers

        self.bill_of_materials = self.create_bill_of_materials()
        self.energy_density_kg_c10 = self.energy_c10 / self.weight * 1e3
        self.energy_density_l_c10 = self.energy_c10 / self.volume
        self.energy_density_kg_c3 = exception_to_none(lambda: self.energy_c3 / self.weight * 1e3)
        self.energy_density_l_c3 = exception_to_none(lambda: self.energy_c3 / self.volume)

        self.cathode_price = self.cathode_price_kg * self.cathode_weight / 1000
        self.anode_price = self.anode_price_kg * self.anode_weight / 1000

        self.price_kg = exception_to_none(lambda: self.price / self.weight * 1e3)
        self.cathode_price_kg = exception_to_none(lambda: self.cathode_price / self.cathode_weight * 1e3)
        self.anode_price_kg = exception_to_none(lambda: self.anode_price / self.anode_weight * 1e3)

        self.price_kwh_c10 = exception_to_none(lambda: self.price / self.energy_c10 * 1e3)
        self.cathode_price_kwh_c10 = exception_to_none(lambda: self.cathode_price / self.energy_c10 * 1e3)
        self.anode_price_kwh_c10 = exception_to_none(lambda: self.anode_price / self.energy_c10 * 1e3)

        self.price_kwh_c3 = exception_to_none(lambda: self.price / self.energy_c3 * 1e3)
        self.cathode_price_kwh_c3 = exception_to_none(lambda: self.cathode_price / self.energy_c3 * 1e3)
        self.anode_price_kwh_c3 = exception_to_none(lambda: self.anode_price / self.energy_c3 * 1e3)

        # Calculate Safety Version
        self.safe_capacity_c10 = self.capacity_c10 * self.safety
        self.safe_energy_c10 = self.energy_c10 * self.safety
        self.safe_energy_density_l_c10 = self.energy_density_l_c10 * self.safety
        self.safe_energy_density_kg_c10 = self.energy_density_kg_c10 * self.safety
        self.safe_capacity_c3 = exception_to_none(lambda: self.capacity_c3 * self.safety)
        self.safe_energy_c3 = exception_to_none(lambda: self.energy_c3 * self.safety)
        self.safe_energy_density_l_c3  = exception_to_none(lambda: self.energy_density_l_c3 * self.safety)
        self.safe_energy_density_kg_c3 = exception_to_none(lambda: self.energy_density_kg_c3 * self.safety)
        self.safe_capacity = self.capacity * self.safety
        self.safe_energy = self.energy * self.safety

        self.safe_price = exception_to_none(lambda: self.price / self.safety)
        self.safe_price_kwh_c10 = exception_to_none(lambda: self.price_kwh_c10 / self.safety)
        self.safe_price_kwh_c3 = exception_to_none(lambda: self.price_kwh_c3 / self.safety)
        self.safe_price_kg = exception_to_none(lambda: self.price_kg / self.safety)
        
        if self.energy_c3 is None or np.isnan(self.energy_c3):
            self.safe_capacity = self.safe_capacity_c10
            self.safe_energy_density_l = self.safe_energy_density_l_c10
            self.safe_energy_density_kg = self.safe_energy_density_kg_c10
        else:
            self.safe_capacity = self.safe_capacity_c3
            self.safe_energy_density_l = self.safe_energy_density_l_c3
            self.safe_energy_density_kg = self.safe_energy_density_kg_c3

        # Pore Volume
        self.separator_porosity = 45
        self.pore_volume = ((cell_layer.anode.get_porosity() / 100 * self.cell_layer.anode.volume * self.cell_format.no_anode_layers * 2) + \
            (self.cell_layer.cathode.get_porosity() / 100 * self.cell_layer.cathode.volume * self.cell_format.no_cathode_layers * 2) + \
            (self.separator_porosity / 100 * self.cell_layer.separator.volume * self.cell_format.no_separator_layers)) / 1e6
        self.pore_volume_ah = self.pore_volume / self.capacity

        m_act_an = self.bill_of_materials.loc["Anode", "weight"] * self.cell_layer.anode.active_material_weight
        a_act_an = self.cell_layer.anode.active_material.get_active_surface() * m_act_an
        self.sei = SolidElectrolyteInterface(0.61, a_act_an)
        self.swelling = FullCellSwelling(self)
        self.cf3_breathing_with_compression = self.swelling.cf3_breathing_with_compression

        # Calculate Cell Layer Characteristics

        self.cell_layer.capacity_c10 = self.capacity_c10/self.cell_format.no_cathode_layers
        self.cell_layer.energy_c10 = self.energy_c10/self.cell_format.no_cathode_layers
        if self.energy_c3 is not None and not np.isnan(self.energy_c3):
            self.cell_layer.capacity_c3 = self.capacity_c3/self.cell_format.no_cathode_layers
            self.cell_layer.energy_c3 = self.energy_c3/self.cell_format.no_cathode_layers
         
        # self.calculate_resistances()
        # 
        # [self.thermal_table,self.thermal_mass] = self.create_thermal_table()
        # self.resistance_rel = app.r_q_nom # input from app
        # self.Resistance = self.resistance_rel/self.capacity

    def create_list_of_materials(self) -> pd.DataFrame:
        cathode_materials = self.cell_layer.cathode.get_recipe()
        cathode_materials['weight'] = cathode_materials['weight_percent'] * self.cathode_weight
        cathode_materials = cathode_materials[['name', 'material', 'weight']]
        cathode_materials['price_kg_edit_id'] = cathode_materials.apply(lambda row: f"material_{row['material'].name}_price_kg", axis = 1)
        cathode_materials['price_kg'] = cathode_materials.apply(lambda row: self.get_price(row['material']), axis = 1)
        self.cathode_price_kg = (cathode_materials['price_kg'] * cathode_materials['weight']).sum() / self.cathode_weight if ~cathode_materials['price_kg'].isna().any() else float('nan')
        cathode = { "name": "Cathode", "material": self.cell_layer.cathode, "weight": self.cathode_weight, "price_kg": self.cathode_price_kg,"is_primary": True }

        anode_materials = self.cell_layer.anode.get_recipe()
        anode_materials['weight'] = anode_materials['weight_percent'] * self.anode_weight
        anode_materials = anode_materials[['name', 'material', 'weight']]
        anode_materials['price_kg_edit_id'] = anode_materials.apply(lambda row: f"material_{row['material'].name}_price_kg", axis = 1)
        anode_materials['price_kg'] = anode_materials.apply(lambda row: self.get_price(row['material']), axis = 1)
        self.anode_price_kg = (anode_materials['price_kg'] * anode_materials['weight']).sum() / self.anode_weight if ~anode_materials['price_kg'].isna().any() else float('nan')
        anode = { "name": "Anode", "material": self.cell_layer.anode, "weight": self.anode_weight, "price_kg": self.anode_price_kg,"is_primary": True }

        other_materials = pd.DataFrame([
            { "name": "Separator", "material": self.cell_layer.separator.material, "weight": self.cell_layer.separator.weight * self.cell_format.no_separator_layers,"is_primary": True },
            { "name": "Current Collector Cathode", "material": self.cell_layer.al_cc.material, "weight": self.cell_layer.al_cc.weight * self.cell_format.no_cathode_layers,"is_primary": True },
            { "name": "Current Collector Anode", "material": self.cell_layer.cu_cc.material, "weight": self.cell_layer.cu_cc.weight * self.cell_format.no_anode_layers,"is_primary": True },
            { "name": "Electrolyte", "material": self.electrolyte.material, "weight": self.electrolyte.weight,"is_primary": True },
            { "name": "Cathode Tab", "material": self.cell_format.cathode_tab.material, "weight": self.cell_format.cathode_tab_weight,"is_primary": True },
            { "name": "Anode Tab", "material": self.cell_format.anode_tab.material, "weight": self.cell_format.anode_tab_weight,"is_primary": True },
            { "name": self.cell_format.housing.name, "material": self.cell_format.housing, "weight": self.cell_format.housing_weight,"is_primary": True }
        ])

        other_materials['price_kg_edit_id'] = other_materials.apply(lambda row: f"material_{row['material'].name}_price_kg" if row['material'] is not None else None, axis = 1)
        other_materials['price_kg'] = other_materials.apply(lambda row: self.get_price(row['material']), axis = 1)
        other_weight = other_materials['weight'].sum()
        other_price_kg = (other_materials['price_kg'] * other_materials['weight']).sum() / other_weight if ~other_materials['price_kg'].isna().any() else float('nan')

        self.price = (self.anode_price_kg * self.anode_weight + self.cathode_price_kg * self.cathode_weight + other_price_kg * other_weight) / 1000
        self.weight = self.anode_weight + self.cathode_weight + other_weight
        cell = pd.DataFrame([
            { "name": "Cell", "material": None, "weight": self.weight, "price_kg": (self.price / self.weight) * 1000 ,"is_primary": True }])
        
        scrap_weight = self.weight * self.scrap
        scrap_price_kg = (self.price / self.weight) * 1000 * self.scrap
        scrap = pd.DataFrame([
            { "name": "Ausschuss", "material": None, "weight": scrap_weight, "price_kg": scrap_price_kg ,"is_primary": True }])

        return pd.concat([
            pd.DataFrame([cathode]),
            cathode_materials,
            pd.DataFrame([anode]),
            anode_materials,
            other_materials,
            scrap,
            cell
            ], axis = 'index').set_index('name')

    def load_excel_prices(self):
        path = os.path.join(os.getcwd(), 'data', 'Preise_2025-05-22.xlsx')
        df = pd.read_excel(path, index_col = 0)
        return df

    def get_price(self, material: Material) -> float:

        if material is None:
            return float('nan')
        
        edit_id = f"material_{material.name}_price_kg"
        custom_price = self.custom_prices.get(edit_id, None)
        if custom_price is not None:
            return custom_price

        value = self.prices[self.business_case].get(material.name, float('nan'))
        unit = self.prices['Einheit'].get(material.name, None)

        # %if so AND it's an Fluid
        if unit == "€/l":
            electrolyte_dens = self.electrolyte.material.density
            return value / electrolyte_dens
            

        # %if so AND its is an Area
        elif unit == "€/m^2":
            separator_areal_weight = self.cell_format.anode_coating_area * 1e-6 / (self.cell_layer.separator.weight * 1e-3) 
            return value * separator_areal_weight

        # %if so AND its is an Energy
        elif unit == "€/kWh":
            return value * self.energy / self.cell_format.housing_weight

        # %if it's a "Teil"
        elif unit == "€/Teil":
            return value

        elif unit == "€/kg":
            return value
        else:
           return value

    def create_bill_of_materials(self) -> pd.DataFrame:
        list_of_materials = self.create_list_of_materials()

        list_of_materials['price_cell'] = (list_of_materials['weight'] / 1000) * list_of_materials['price_kg']
        list_of_materials['price_c10'] = list_of_materials['price_cell'] / self.energy_c10 * 1000
        list_of_materials['price_c3']  = list_of_materials['price_cell'] / (float('nan') if self.energy_c3 is None else self.energy_c3) * 1000
        columns = ['weight', 'price_kg_edit_id', 'price_kg', 'price_cell', 'price_c10', 'price_c3','is_primary']
        list_of_materials = list_of_materials[columns]

        return list_of_materials.replace({ np.nan: None })
    
    def get_bom_table(self) -> TableModel:
        has_is_primary = 'is_primary' in self.bill_of_materials.columns
        return TableModel(
            headers = [ 'Name', 'Gewicht [g/Cell]', 'Preis [€/kg]', 'Preis [€/Cell]', 'Preis [€/kWh] (C/10)', 'Preis [€/kWh] (C/3)' ],
            rows = [
                TableRowModel(
                    cells = [
                        TableCellModel(value = self.bill_of_materials.index[row_index], unit = None),
                        TableCellModel(value = self.bill_of_materials.iloc[row_index].loc['weight'], unit = 'g'),
                        TableCellModel(value = self.bill_of_materials.iloc[row_index].loc['price_kg'], unit = '€', editable = None if self.bill_of_materials.iloc[row_index].loc['price_kg_edit_id'] is None else TableCellEditModel(id = self.bill_of_materials.iloc[row_index].loc['price_kg_edit_id'], min = 0)),
                        TableCellModel(value = self.bill_of_materials.iloc[row_index].loc['price_cell'], unit = '€'),
                        TableCellModel(value = self.bill_of_materials.iloc[row_index].loc['price_c10'], unit = '€'),
                        TableCellModel(value = self.bill_of_materials.iloc[row_index].loc['price_c3'], unit = '€')
                    ],
                    is_primary = bool(self.bill_of_materials.iloc[row_index].loc['is_primary']) if has_is_primary and 'is_primary' in self.bill_of_materials.iloc[row_index] else False
                )
                for row_index in range(len(self.bill_of_materials.index))
            ]
        )

    def get_aging_table(self, aging_table_edit_values: dict) -> TableModel:
        coulomb_efficiency = self.balancing.cell_formations.get_coulomb_efficiency()

        columns = [
            ('Vor Formierung', 1 / coulomb_efficiency * 100, None),
            ('Nach Formierung', 100, None),
            ('End of Life', 80, None),
            ('End of Warranty', 50, None),
            ('Look-Up', 90, 'aging_soh_lookup_id')
        ]

        def get_column(soh: float, edit_id: Optional[str]):
            # if soh id editble, check if it has been changed
            soh = soh if edit_id is None else (aging_table_edit_values[edit_id] if edit_id in aging_table_edit_values else soh)

            # calculate parameters for soh
            capacity_loss = (1 / coulomb_efficiency - 1 + (1 - soh / 100)) * self.capacity_c10
            sei_volume = capacity_loss * self.sei.growth_per_charge
            sei_thickness = capacity_loss * self.sei.growth_nm_per_charge
            electrolyte_loss = capacity_loss * self.electrolyte.material.sei_loss_ah
            pore_volume = self.pore_volume - sei_volume
            electrolyte_volume = self.electrolyte.volume - electrolyte_loss
            electrolyte_buffer = electrolyte_volume - pore_volume
            anode_volume0 = 2 * self.cell_format.no_anode_layers * self.cell_layer.anode.volume * 1e-6
            anode_pore_volume0 = 2 * self.cell_format.no_anode_layers * self.cell_layer.anode.get_porosity() / 100 * self.cell_layer.anode.volume * 1e-6
            anode_pore_volume = anode_pore_volume0 - sei_volume
            anode_porosity = anode_pore_volume / anode_volume0 * 100

            # return structured data for table rendering
            return [(soh, edit_id), (pore_volume, None), (electrolyte_volume, None), (electrolyte_buffer, None), (sei_volume, None), (anode_porosity, None), (sei_thickness, None)]

        row_headers = ['State of Health [%]', 'Porenvolumen [ml]', 'Elektrolytvolumen [ml]', 'Elektrolytpuffer [ml]', 'SEI Volumen [ml]', 'Anoden Porosität [%]', 'SEI Dicke [nm]']
        data = [(header, get_column(soh, edit_id)) for (header, soh, edit_id) in columns]

        return TableModel(
            headers = ['Beschreibung'] + [header for (header, column) in data],
            rows = [
                TableRowModel(
                    cells = [TableCellModel(value = row_headers[row_index])] + [
                        TableCellModel(value = column[row_index][0], unit = None, editable = None if column[row_index][1] is None else TableCellEditModel(id = column[row_index][1], min = 0, max = 100))
                        for (header, column) in data
                    ]
                )
                for row_index in range(len(row_headers))
            ]
        )

        data = { name: get_column(soh) for (name, soh) in columns }
        return pd.DataFrame(data, index = ['State of Health [%]', 'Porenvolumen [ml]', 'Elektrolytvolumen [ml]', 'Elektrolytpuffer [ml]', 'SEI Volumen [ml]', 'Anoden Porosität [%]', 'SEI Dicke [nm]'])

    # def calculate_resistances(self):
    #     # Prebuild Table
    #     resistance_names = ["Gesamt","Kathode","Anode","Cu Stromableiter","Al Stromableiter","Cu Tab","Al Tab"]
    #     table_data = np.empty(len(resistance_names))

    #     # Calculate Resistances
    #     table_data[2] = self.cell_layer.cathode.get_resistance() / (self.cell_format.get_no_cathode_layers * 2 * self.cell_format.cathode_coating_area / 1e2 ) * 1e3 #%mOhm
    #     table_data[3] = self.cell_layer.anode.get_resistance() / (self.cell_format.cathode_layers * 2 * self.cell_format.cathode_coating_area / 1e2) * 1e3; #%mOhm 
    #     table_data[4] = self.cell_layer.cu_cc.specific_resistance * self.cell_format.anode_coating_length / 1e3 / (self.cell_format.cathode_coating_width * self.cell_layer.cu_cc.thickness / 1e3 * self.cell_format.cathode_layers ) * 1e3; #%mOhm
    #     table_data[5] = self.cell_layer.al_cc.specific_resistance * self.cell_format.anode_coating_length / 1e3 / (self.cell_format.cathode_coating_width * self.cell_layer.al_cc.thickness / 1e3 * self.cell_format.cathode_layers ) * 1e3; #%mOhm
    #     if not np.isnan(self.cell_format.get_anode_tab().resistance):
    #         table_data[6] = self.cell_format.get_anode_tab().resistance
    #     if not np.isnan(self.cell_format.get_cathode_tab().resistance):
    #         table_data[7] = self.cell_format.get_cathode_tab().resistance
    #     table_data[1] = sum(table_data[2:-1])
    #     self.resistances = table_data
    #     self.resistances.properties.variable_names = resistance_names #Not sure what this part does

    #     return
    
    # # Not sure about this function
    # def get_component_mass(self, description): 
    #     mind = ((x == description) for x in self.bill_of_materials[:,1]) #?
    #     mass = sum(self.bill_of_materials["Weight / g"][mind])
    #     return mass
    
    # def create_thermal_table(self) -> pd.DataFrame: 
    #     components = ["Cathode","Anode","Kupfer","Aluminium","Separator","Electrolyte","Gehäuse"]
    #     cp_values = [810,705,385,890,700,1642,1900] * 1e-3; #%J/gK
    #     weights = np.empty(len(components))
    #     for i in range (len(components)):
    #         match components(i):
    #             case "Kupfer":
    #                 weights[i] = self.get_component_mass("Kupfer Stromableiter") + self.get_component_mass("Anode Tab")
    #             case "Aluminium":
    #                 weights[i] = self.get_component_mass("Aluminium Stromableiter") + self.get_component_mass("Cathode Tab")
    #             case "Gehäuse":
    #                 weights[i] = self.get_component_mass("Pouch Foil") + self.get_component_mass("Housing")
    #             case _:
    #                 weights[i] = self.get_component_mass(components[i])

    #     thermal_mass = sum(weights * cp_values)
    #     cp_cell = thermal_mass / sum(weights)
    #     cp_values = [cp_values, cp_cell]
    #     weights = [weights, sum(weights)]
    #     components = [components, "Zelle"]
    #     thermal_table = pd.DataFrame[weights, cp_values, 'VariableNames' , ["Masse [g]" , "Cp [J/(gK)]"] ,'RowNames', components]
        
    #     return thermal_table
    