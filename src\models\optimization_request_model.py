from dataclasses import dataclass
from typing import Optional

from models.cell_design_model import CellDesignModelWithMetadata
from models.optimization_result_model import OptimizationHistory

@dataclass(kw_only = True)
class OptimizationRequestVariable:
    id: str
    min_value: float
    max_value: float
    step_size: Optional[float] = None
    original: Optional[float] = None

@dataclass(kw_only = True)
class OptimizationSettingsRequestModel:
    algorithm: str  # AlgorithmType
    stop_criterion_type: Optional[str] = None  # StopCriterionType
    stop_criterion_value: Optional[int] = None
    variables: list[OptimizationRequestVariable]
    population_size: Optional[int] = None
    objective: str
    history: Optional[OptimizationHistory] = None
    status: Optional[str] = None  # OptimizationStatus
    optimization_target: Optional[str] = None
    num_initial_points: Optional[int] = 10
    num_iterations: Optional[int] = 20

@dataclass
class OptimizationRequestModel:
    cell_design: CellDesignModelWithMetadata
    optimization: OptimizationSettingsRequestModel
