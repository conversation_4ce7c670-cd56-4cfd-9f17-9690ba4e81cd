from dataclasses import dataclass
from typing import Optional
from electrochemistry.material import Material, MaterialType
from electrochemistry.material_collection import material_collection

@dataclass
class Component:
    material: Material

    thickness: float
    volume: float
    area: float
    weight: float
    resistance: float

    #@overloaded
    def __init__(self, material: Material, thickness: Optional[float] = None, area: Optional[float] = None, resistance: Optional[float] = None, weight: Optional[float] = None):
        self.material = material
        self.resistance = resistance
        
        if weight is None:
            self.set_geometry(thickness, area)
        else:
            self.weight = weight

    def set_thickness(self, thickness: float):
        self.thickness = thickness
        if self.thickness is not None and self.area is not None:
            self.volume = self.thickness * self.area
            self.weight = self.volume * self.material.density * 1e-6 # mm^2*um*g/cm^3
        else:
            self.volume = None
            self.weight = None

    def set_geometry(self, thickness: float, area: float):
        self.thickness = thickness
        self.area = area
        if self.thickness is not None and self.area is not None:
            self.volume = self.thickness * self.area
            self.weight = self.volume * self.material.density * 1e-6 # mm^2*um*g/cm^3
        else:
            self.volume = None
            self.weight = None

    def from_dict(dict: dict) -> 'Component':
        material_id = dict.get('material_id')
        material = material_collection.get_material(material_id)

        return Component(
            material = material,
            thickness = dict.get('thickness'),
            area = dict.get('area'),
            resistance = dict.get('resistance'),
            weight = dict.get('weight')
        )
