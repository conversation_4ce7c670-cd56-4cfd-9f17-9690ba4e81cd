from dataclasses import dataclass
from typing import Literal, Optional, List

StopCriterionType = Literal['n_gen', 'n_eval', 'n_iter', 'time']
AlgorithmType = Literal['nsga2', 'pso']

@dataclass(kw_only = True)
class OptimizationSettingParameter:
    id: str
    name: Optional[str] = None
    unit: Optional[str] = None

@dataclass(kw_only = True)
class OptimizationSettingVariable(OptimizationSettingParameter):
    unit: Optional[str] = None

@dataclass(kw_only = True)
class ObjectiveVariables:
    objective: OptimizationSettingParameter
    stop_criterions: List[OptimizationSettingParameter]  # list[StopCriterionType]
    algorithms: List[OptimizationSettingParameter]  # list[AlgorithmType]
    variables: List[OptimizationSettingVariable]
    optimization_target: OptimizationSettingParameter

OptimizationSettingsModel = List[ObjectiveVariables]
