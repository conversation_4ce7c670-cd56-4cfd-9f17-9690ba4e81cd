import math
import numpy as np
import pandas as pd
from typing import Optional
from electrochemistry.active_material_blend import ActiveMaterialBlend
from electrochemistry.formation_table import Formation, FormationTable
from models.chart_model import BoxAnnotation, BoxAnnotationLabel, ChartModel
from models.table_model import TableModel
from utils.interpolate import interpolate

class Balancing(object):

    anode: ActiveMaterialBlend
    cathode: ActiveMaterialBlend
    u_min: Optional[float]
    u_max: Optional[float]
    np_ratio_first: Optional[float]

    cell_formations_max: FormationTable
    cell_formations: FormationTable
    u_nom: pd.Series

    warning: Optional[str]
    u_min_lower_bound: float
    u_min_upper_bound: float
    u_max_lower_bound: float
    u_max_upper_bound: float
    hysteresis: Optional[float]

    def __init__(self, anode: ActiveMaterialBlend, cathode: ActiveMaterialBlend, u_min: Optional[float], u_max: Optional[float], np_ratio_first: Optional[float]):
        self.anode = anode
        self.cathode = cathode
        self.u_min = u_min
        self.u_max = u_max
        self.np_ratio_first = np_ratio_first
        self.warning = None
        self._overwrite_np_ratio = None
        self.calculate_cycles()

    def overwrite_np_ratio_rev(self, value):
        self._overwrite_np_ratio = value

    def get_np_mass_ratio(self) -> Optional[float]:
        if self.np_ratio_first is not None:
            return self.np_ratio_first * self.cathode.formations.get_formation_first_charge().capacity / self.anode.formations.get_formation_first_charge().capacity
        else:
            return None

    def get_np_ratio_rev(self) -> Optional[float]:
        if self._overwrite_np_ratio is not None:
            return self._overwrite_np_ratio
        np_mass_ratio = self.get_np_mass_ratio()
        if np_mass_ratio is not None:
            return np_mass_ratio * (self.anode.formations.get_formation_c_10_discharge().capacity / self.cathode.formations.get_formation_c_10_discharge().capacity)
        else:
            return None

    def get_summary_table(self) -> TableModel:
        return self.cell_formations.get_balancing_summary_table()

    def get_chart_data(self, n_samples: int) -> ChartModel:
        return FormationTable.get_prefixed_chart_data({
            'cell': ('Zelle', self.cell_formations),
            'cathode': ('Kathode', self.cathode.formations),
            'anode': ('Anode', self.anode.formations.scale_q(self.get_np_mass_ratio()))
        }, n_samples)

    def get_chart_annotations(self) -> list[BoxAnnotation]:
        anode_q_max = self.anode.formations.get_formation_second_charge().q_max * self.get_np_mass_ratio()
        cathode_q_max = self.cathode.formations.get_formation_second_charge().q_max
        delta_q = anode_q_max - cathode_q_max

        delta_q_left = self.anode.formations.get_formation_first_discharge().q_min * self.get_np_mass_ratio() - self.cathode.formations.get_formation_first_discharge().q_min

        right = BoxAnnotation(
            label = BoxAnnotationLabel(
                display = True,
                content = 'Anoden Puffer' if delta_q > 0 else 'Lithium Plating'
            ),
            id = 'right_annotation',
            type = 'box',
            x_min = cathode_q_max if delta_q > 0 else anode_q_max,
            x_max = cathode_q_max + delta_q if delta_q > 0 else anode_q_max - delta_q,
            draw_time = 'beforeDatasetsDraw',
            background_color = 'rgba(0, 178, 0, 0.3)'
        )

        if delta_q_left < 0:
            left = BoxAnnotation(
                label = BoxAnnotationLabel(
                    display = True,
                    content = 'Anoden Puffer' if delta_q > 0 else 'Lithium Plating'
                ),
                id = 'left_annotation',
                type = 'box',
                x_min = 0,
                x_max = -delta_q_left,
                draw_time = 'beforeDatasetsDraw',
                background_color = 'rgba(0, 178, 0, 0.3)'
            )
        else:
            left = None

        return [right] if left is None else [left, right]

    def calculate_cycles(self):
        formation_descriptions = [
            'Formierung 1. Laden',
            'Formierung 1. Entladen',
            'Formierung 2. Laden',
            'C/10 Entladen', # swapped
            'C/10 Laden', # swapped
            'C/3 Entladen',
            'C/2 Entladen',
            '1C Entladen'
        ]

        formation_descriptions.extend((formation.description for formation in self.anode.formations.formations if formation.cycle is not None and formation.description not in formation_descriptions))
        formation_descriptions.extend((formation.description for formation in self.cathode.formations.formations if formation.cycle is not None and formation.description not in formation_descriptions))

        cell_formations: list[Formation] = [None for _ in range(len(formation_descriptions))]
        cell_formations_max: list[Formation] = [None for _ in range(len(formation_descriptions))]

        finished = False
        while not finished:
            # This outer loop is to narrow down u_min and u_max.
            # If q_end can't be interpolated for a specific formation, u_min and u_max are updated
            # accordingly and the calculation starts over until all formations are valid.
            q_start = 0
            v_min = 0
            v_max = 10

            for (description_index, description) in enumerate(formation_descriptions):
                cathode_formation = self.cathode.formations.get_formation(description)
                anode_formation = self.anode.formations.get_formation(description)
                if cathode_formation is None or cathode_formation.cycle is None or anode_formation is None or anode_formation.cycle is None:
                    cell_formation = Formation(description, None)
                    cell_formation_max = Formation(description, None)
                else:
                    if description in [ 'Formierung 1. Laden', 'Formierung 2. Laden', 'C/10 Laden' ]:
                        charging = True
                    else:
                        charging = False

                    # 1. Maximum possible FullCell data (without voltage limits)

                    # a) Span capacity vector (overlap anode and cathode)
                    q_cell_start = max(anode_formation.cycle['LadungMAhg'].min() * self.get_np_mass_ratio(), cathode_formation.cycle['LadungMAhg'].min())
                    q_cell_end = min(anode_formation.cycle['LadungMAhg'].max() * self.get_np_mass_ratio(), cathode_formation.cycle['LadungMAhg'].max())
                    q_cell = pd.Series(range(0, 1000)) * ((q_cell_end - q_cell_start) / 999) + q_cell_start

                    # b) Interpolate Anode Voltage
                    # In Matlab q was not monotonized, but duplicates were dropped from q.
                    # For monotonous (but not strict monotonous) q values the effect is the same.
                    # For non-monotonous values the effect is different, but otherwise the interpolation will fail.
                    anode_formation_monotonized_q = anode_formation.monotonize_q()

                    v_anode = interpolate(q_cell, anode_formation_monotonized_q.cycle['LadungMAhg'] * self.get_np_mass_ratio(), anode_formation_monotonized_q.cycle['SpannungV'], default=float('nan'))

                    # c) Interpolate Cathode Voltage
                    # In Matlab q was not monotonized, but duplicates were dropped from q.
                    # For monotonous (but not strict monotonous) q values the effect is the same.
                    # For non-monotonous values the effect is different, but otherwise the interpolation will fail.
                    cathode_formation_monotonized_q = cathode_formation.monotonize_q()

                    v_cathode = interpolate(q_cell, cathode_formation_monotonized_q.cycle['LadungMAhg'], cathode_formation_monotonized_q.cycle['SpannungV'], default=float('nan'))

                    # d) Calculate Cell Voltage
                    v_cell = v_cathode - v_anode

                    # e) Assign Valid Indices of Charge and Voltage to the Full Cell
                    cell_formation_max = Formation(description, pd.DataFrame({
                        'LadungMAhg': q_cell,
                        'SpannungV': v_cell
                    })).monotonize_u()

                    # f) Update Vmin and Vmax
                    if charging and cell_formation_max.cycle['SpannungV'].max() < v_max:
                        v_max = cell_formation_max.cycle['SpannungV'].max()
                    elif (not charging) and cell_formation_max.cycle['SpannungV'].min() > v_min:
                        v_min = cell_formation_max.cycle['SpannungV'].min()

                    # 2. Cut at voltage limits
                    # a) Span Capacity Vector
                    if description in [ 'Formierung 1. Laden', 'Formierung 1. Entladen', 'Formierung 2. Laden' ]:
                        if charging:
                            q_end = interpolate([self.u_max], cell_formation_max.cycle['SpannungV'], cell_formation_max.cycle['LadungMAhg'], default=float('nan'))[0]
                        else:
                            q_end = interpolate([self.u_min], cell_formation_max.cycle['SpannungV'], cell_formation_max.cycle['LadungMAhg'], default=float('nan'))[0]
                    elif description in [ 'C/10 Laden' ]:
                        cell_formation_max_c_10_discharge = next(filter(lambda f: f.description == 'C/10 Entladen', cell_formations_max), None) 
                        q_start = interpolate([self.u_min], cell_formation_max_c_10_discharge.cycle['SpannungV'], cell_formation_max_c_10_discharge.cycle['LadungMAhg'], default=float('nan'))[0]
                        q_end = interpolate([self.u_max], cell_formation_max.cycle['SpannungV'], cell_formation_max.cycle['LadungMAhg'], default=float('nan'))[0]
                        pass
                    else:
                        # Start every rate test at same capacity
                        cell_formation_max_second_charge = next(filter(lambda f: f.description == 'Formierung 2. Laden', cell_formations_max), None) 
                        q_start = interpolate([self.u_max], cell_formation_max_second_charge.cycle['SpannungV'], cell_formation_max_second_charge.cycle['LadungMAhg'], default=float('nan'))[0]
                        q_end = interpolate([self.u_min], cell_formation_max.cycle['SpannungV'], cell_formation_max.cycle['LadungMAhg'], default=float('nan'))[0]
                        pass

                    if math.isnan(q_end):
                        if self.warning is None:
                            self.warning = 'Achtung! Umax außerhalb der Halbzelldaten' if charging else 'Achtung! Umin außerhalb der Halbzelldaten'
                        
                        if charging:
                            self.u_max = cell_formation_max.cycle['SpannungV'].max()
                        else:
                            self.u_min = cell_formation_max.cycle['SpannungV'].min()

                        break

                    q_vec = pd.Series(range(0, 1000)) * ((q_end - q_start) / 999) + q_start

                    # b) Interpolate Voltage
                    cell_formation = Formation(description, pd.DataFrame({
                        'LadungMAhg': q_vec,
                        'SpannungV': interpolate(q_vec, cell_formation_max.cycle['LadungMAhg'], cell_formation_max.cycle['SpannungV'])
                    }))
                    # TODO: open question: should we monotonize cell_formation on SpannungV

                    q_start = q_end

                cell_formations[description_index] = cell_formation
                cell_formations_max[description_index] = cell_formation_max

                if description == formation_descriptions[-1]:
                    finished = True

        self.cell_formations = FormationTable(cell_formations)
        self.cell_formations_max = FormationTable(cell_formations_max)

        self.u_min_lower_bound = v_min
        self.u_min_upper_bound = self.u_max - 1e-2
        self.u_max_lower_bound = self.u_min + 1e-2
        self.u_max_upper_bound = v_max

        # h) Determine Nominal voltage
        # this happens inside the init of the formation now
        
        c_10_charge_u_nom = self.cell_formations.get_formation_c_10_charge().u_nom
        c_10_discharge_u_nom = self.cell_formations.get_formation_c_10_discharge().u_nom
        self.hysteresis = (c_10_charge_u_nom - c_10_discharge_u_nom) / 2 if c_10_charge_u_nom is not None and c_10_discharge_u_nom is not None else None

        # Step 6: Determine Capacities
        # this happens inside the init of the formation now
