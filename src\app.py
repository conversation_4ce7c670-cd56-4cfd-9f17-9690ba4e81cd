from flask import Flask
from flask_cors import CORS
from active_material_blueprint import active_material_blueprint
from inactive_material_blueprint import inactive_material_blueprint
from business_case_blueprint import business_case_blueprint
from cell_format_blueprint import cell_format_blueprint
from cell_design_blueprint import cell_design_blueprint
from cell_design_metrics_blueprint import cell_design_metrics_blueprint
from cell_design_export_blueprint import cell_design_export_blueprint
from autocomplete_blueprint import autocomplete_blueprint
from optimization_blueprint import optimization_blueprint
from optimization_settings_blueprint import optimization_settings_blueprint
from tolerance_settings_blueprint import tolerance_settings_blueprint
from tolerance_blueprint import tolerance_blueprint
import database

app = Flask(__name__)
cors = CORS(app)

app.config.from_pyfile('settingslocal.py')
app.register_blueprint(active_material_blueprint)
app.register_blueprint(inactive_material_blueprint)
app.register_blueprint(business_case_blueprint)
app.register_blueprint(cell_format_blueprint)
app.register_blueprint(cell_design_blueprint)
app.register_blueprint(cell_design_metrics_blueprint)
app.register_blueprint(cell_design_export_blueprint)
app.register_blueprint(autocomplete_blueprint)
app.register_blueprint(optimization_blueprint)
app.register_blueprint(optimization_settings_blueprint)
app.register_blueprint(tolerance_settings_blueprint)
app.register_blueprint(tolerance_blueprint)

@app.get('/api/health')
def health():
    return ('OK', 200)

@app.get('/api/connection')
def connection():
    return {
        'fqdn': database.fqdn_option,
        'port': database.port_option,
        'database': database.database_option,
        'username': database.username_option,
        'password': database.password_option,
        'passwordSecretArn': database.password_secret_arn_option,
        'tlsCAFile': database.tls_ca_file_option,
    }
