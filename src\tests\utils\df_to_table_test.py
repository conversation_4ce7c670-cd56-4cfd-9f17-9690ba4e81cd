import unittest
import pandas as pd
from models.table_model import TableCellModel

from utils.df_to_table import df_to_table

class TestInterpolate(unittest.TestCase):

    def test_df_to_table(self):
        df = pd.DataFrame([
            { 'Col1': 1, 'Col2': 2, 'ColA': 'A', 'Col4': 4 },
            { 'Col1': 42, 'Col2': 4342, 'ColA': 943, 'Col4': 951 },
            { 'Col1': 12, 'Col2': 325, 'ColA': 345, 'Col4': 5235 },
            { 'Col1': 64, 'Col2': 'G', 'ColA': 234, 'Col4': 12 },
            { 'Col1': 53.5, 'Col2': 321, 'ColA': 21, 'Col4': 524 }
        ])

        table = df_to_table(df)
        self.assertListEqual(table['headers'], ['Col1', 'Col2', 'ColA', 'Col4'])
        self.assertListEqual(table['rows'][0]['cells'], [TableCellModel(value = 1, unit = None), TableCellModel(value = 2, unit = None), TableCellModel(value = 'A', unit = None), TableCellModel(value = 4, unit = None)])
        self.assertListEqual(table['rows'][1]['cells'], [TableCellModel(value = 42, unit = None), TableCellModel(value = 4342, unit = None), TableCellModel(value = 943, unit = None), TableCellModel(value = 951, unit = None)])
        self.assertListEqual(table['rows'][2]['cells'], [TableCellModel(value = 12, unit = None), TableCellModel(value = 325, unit = None), TableCellModel(value = 345, unit = None), TableCellModel(value = 5235, unit = None)])
        self.assertListEqual(table['rows'][3]['cells'], [TableCellModel(value = 64, unit = None), TableCellModel(value = 'G', unit = None), TableCellModel(value = 234, unit = None), TableCellModel(value = 12, unit = None)])
        self.assertListEqual(table['rows'][4]['cells'], [TableCellModel(value = 53.5, unit = None), TableCellModel(value = 321, unit = None), TableCellModel(value = 21, unit = None), TableCellModel(value = 524, unit = None)])

    def test_df_to_table_with_index(self):
        df = pd.DataFrame([
            { 'Col1': 1, 'Col2': 2, 'ColA': 'A', 'Col4': 4 },
            { 'Col1': 42, 'Col2': 4342, 'ColA': 943, 'Col4': 951 },
            { 'Col1': 12, 'Col2': 325, 'ColA': 345, 'Col4': 5235 },
            { 'Col1': 64, 'Col2': 'G', 'ColA': 234, 'Col4': 12 },
            { 'Col1': 53.5, 'Col2': 321, 'ColA': 21, 'Col4': 524 }
        ]).set_index('Col1')

        table = df_to_table(df, 'ColIdx')
        self.assertListEqual(table['headers'], ['ColIdx', 'Col2', 'ColA', 'Col4'])
        self.assertListEqual(table['rows'][0]['cells'], [TableCellModel(value = 1, unit = None), TableCellModel(value = 2, unit = None), TableCellModel(value = 'A', unit = None), TableCellModel(value = 4, unit = None)])
        self.assertListEqual(table['rows'][1]['cells'], [TableCellModel(value = 42, unit = None), TableCellModel(value = 4342, unit = None), TableCellModel(value = 943, unit = None), TableCellModel(value = 951, unit = None)])
        self.assertListEqual(table['rows'][2]['cells'], [TableCellModel(value = 12, unit = None), TableCellModel(value = 325, unit = None), TableCellModel(value = 345, unit = None), TableCellModel(value = 5235, unit = None)])
        self.assertListEqual(table['rows'][3]['cells'], [TableCellModel(value = 64, unit = None), TableCellModel(value = 'G', unit = None), TableCellModel(value = 234, unit = None), TableCellModel(value = 12, unit = None)])
        self.assertListEqual(table['rows'][4]['cells'], [TableCellModel(value = 53.5, unit = None), TableCellModel(value = 321, unit = None), TableCellModel(value = 21, unit = None), TableCellModel(value = 524, unit = None)])
