from dataclasses import dataclass
from typing import Optional
import pandas as pd
from electrochemistry.active_material_blend import ActiveMaterialBlend
from electrochemistry.component import Component
from electrochemistry.material import Material, MaterialType, MaterialWeight, lithium

# Anode:
# { app.AnodeBlend,   app.AnodeBlend.Prelith.Lithium, app.Binder_PAA_Aquacharge, app.Leitruss_C65, app.CNT }

# Cathode:
# { app.CathodeBlend,                                 app.Binder_5130_Solef,     app.Leitruss_C65, app.CNT }

class Electrode:
    """
    An Electrode consists of:
     - ActiveMaterialBlend (anode or cathode)
     - Lithium (only for anode)
     - Binder (anode: PAA Aquacharge, cathode: 5130 Solef)
     - Leitruss C65,
     - CNT
    """

    active_material: ActiveMaterialBlend
    active_material_weight: float

    binder_materials: list[MaterialWeight]
    conductive_additive_materials: list[MaterialWeight]

    lithium_material: Material
    lithium_weight: float

    substrate: Component

    density: float

    areal_capacity: Optional[float]
    coating_thickness: Optional[float]
    loading: Optional[float]

    volume: Optional[float]
    width: Optional[float]

    def __init__(
            self,
            active_material: ActiveMaterialBlend, active_material_weight: float,
            binder_materials: list[MaterialWeight],
            conductive_additive_materials: list[MaterialWeight],
            lithium_material: Material, lithium_weight: float,
            density: float
            ):

        weight_sum = active_material_weight + sum([m.weight for m in binder_materials]) + sum([m.weight for m in conductive_additive_materials]) + lithium_weight
        
        self.active_material = active_material
        self.active_material_weight = active_material_weight / weight_sum

        self.binder_materials = [MaterialWeight(m.material, m.weight / weight_sum) for m in binder_materials]

        self.conductive_additive_materials = [MaterialWeight(m.material, m.weight / weight_sum) for m in conductive_additive_materials]

        self.lithium_material = lithium_material
        self.lithium_weight = lithium_weight / weight_sum

        self.density = density

        self.coating_thickness = None

        w_active_material = self.active_material_weight + self.lithium_weight

    def set_geometry(self, thickness: float, area: float):
        self.thickness = thickness
        self.area = area
        if self.coating_thickness is not None and self.area is not None:
            self.volume = self.coating_thickness * self.area
            self.weight = self.volume * self.density * 1e-6 # mm^2*um*g/cm^3
        else:
            self.volume = None
            self.weight = None
    
    
    def set_areal_capacity(self, areal_capacity: float):
        self.areal_capacity = areal_capacity
        if self.areal_capacity is not None:
            self.coating_thickness = self.areal_capacity / self.get_volumetric_capacity() * 1e4; # cm -> µm
            self.loading = self.areal_capacity / self.active_material.formations.get_formation_c_10_discharge().capacity / (self.active_material_weight + self.lithium_weight) * 1e3 # mg/cm²
        else:
            self.coating_thickness = None
            self.loading = None

    def get_loading(self):
        return self.areal_capacity / self.active_material.formations.get_formation_c_10_discharge().capacity / (self.active_material_weight + self.lithium_weight) * 1e3 # mg/cm²

    def set_loading(self, loading: float):
        self.loading = loading
        self.areal_capacity = self.loading / 1e3 * self.active_material.formations.get_formation_c_10_discharge().capacity / (self.active_material_weight + self.lithium_weight)
        if self.areal_capacity is not None:
            self.coating_thickness = self.areal_capacity / self.get_volumetric_capacity() * 1e4; # cm -> µm
        else:
            self.coating_thickness = None

    def get_composite_density(self) -> float:
        # we can assume, that all weights are normated to 1
        return 1 / sum([
            self.active_material_weight / self.active_material.composite_density,
            sum([m.weight / m.material.density for m in self.binder_materials]),
            sum([m.weight / m.material.density for m in self.conductive_additive_materials]),
            self.lithium_weight / lithium.density
        ])

    def get_recipe(self) -> pd.DataFrame:
        materials: list[Material] = [m.material for m in self.active_material.materials] + \
            [m.material for m in self.binder_materials] + \
            [m.material for m in self.conductive_additive_materials]

        weights: list[float] = [m.weight * self.active_material_weight for m in self.active_material.materials] + \
            [m.weight for m in self.binder_materials] + \
            [m.weight for m in self.conductive_additive_materials]

        if self.lithium_material is not None:
            materials.append(self.lithium_material)
            weights.append(self.lithium_weight)

        return pd.DataFrame({
            'type': [m.type for m in materials],
            'name': [m.name for m in materials],
            'weight_percent': weights,
            'density': [m.density for m in materials],
            'material': materials
            })
    
    def get_active_material_weight_without_lithium(self) -> float:
        return self.active_material_weight / sum([self.active_material_weight, sum([m.weight for m in self.binder_materials]), sum([m.weight for m in self.conductive_additive_materials])])

    def get_porosity(self) -> Optional[float]:
        if self.density is not None:
            return (1 - self.density / self.get_composite_density()) * 100
        else:
            return None
    
    def get_volumetric_capacity(self) -> Optional[float]:
        if self.density is not None:
            return self.active_material.formations.get_formation_c_10_discharge().capacity * self.density * (self.active_material_weight + self.lithium_weight)
        else:
            return None
    
    def get_thickness(self) -> float:
        return self.coating_thickness * 2 + self.substrate.thickness

    def get_resistance(self) -> float:
        return self.active_material.get_resistance() / (self.loading / 1e3 * (self.active_material_weight + self.lithium_weight)) # Ohm*cm²
