import pandas as pd
import openpyxl
from electrochemistry.electrolyte import Electrolyte
from electrochemistry.material import Material
from electrochemistry.cell_format import CellFormat
from electrochemistry.component import separator
import numpy as np
from models.cell_design_model import CellDesignModel
from electrochemistry.full_cell import FullCell
 
class BillOfMaterials(object):


    # def __init__(self):

    def load_excel_prices(self):
        # path = "C:/User Documents/cellforce/dmgmt-cell-o-mat-2.0-backend/data/"
        path = "../data/Preise_2025-05-22.xlsx"
        # wb = openpyxl.load_workbook(path)
        # sheet = wb.active

        # pandas df 

        df = pd.read_excel(path)
        return df

    
    def set_bom_prices(self): 

        
        

        ### from BOMTable ??? ###


        prices = self.load_excel_prices()

        # create empty BOMTable for store result  and clarify BOMTable
        bom_table = pd.DataFrame()
        df = bom_table ### 
        key = df.loc[:, 'Name']
        vals = df.loc[:, 'Price / €/kg']
        
       
        input_value = Material.name 
        anode_sum = 0
        cathode_sum = 0
        anode_check = 0
        cathode_check = 0
        anode_index = 0
        cathode_index = 0

        # % fill first column (price / Kg) for dataframe
        for i in range (1, (len(vals) + 1)):
            # % create Sum at the End
            # % for NaN: omitnan
            if i == ((len(vals) + 1)):

                # % Create Sum of All
                temp = vals[1:i] 
                temp = temp.dropna() #?
                vals.iloc[i+1] = sum(temp)

                # % Create Sum of Anode and Cathode
                # % do it AFTER the Sum of all
                vals.iloc[anode_index] = anode_sum
                vals.iloc[cathode_index] = cathode_sum

                break
        # % check if key is in price-list
            if np.isin(key.loc[i+1, "Name"], prices["Name"]):
                
                # %if so AND it's an Fluid
                if prices.loc[prices['Name'] == key.loc[i+1, "Name"], "Einheit"] == "€/l":
                    
                    electrolyte_dens = Electrolyte.density
                    vals[i+1] =  prices.loc[prices['Name'] == key.loc[i+1, "Name"], input_value] / electrolyte_dens
                    

                # %if so AND its is an Area
                elif prices.loc[prices['Name'] == key.loc[i+1, "Name"], "Einheit"] == "€/m^2":
                    separator_areal_weight = CellFormat.anode_coating_area * 1e-6 / separator.weight * 1e-3 
                    vals[i+1] =  prices.loc[prices['Name'] == key.loc[i+1, "Name"], input_value] * separator_areal_weight

                # %if so AND its is an Energy
                elif prices.loc[prices['Name'] == key.loc[i+1, "Name"], "Einheit"] == "€/kWh":
                    # %Quick TroubleShoot
                    if not CellFormat.housing_weight: #isempty(app.CellFormat.HousingWeight)
                        CellFormat = CellFormat.calculate_weight
                    vals[i+1] =  prices.loc[prices['Name'] == key.loc[i+1, "Name"], input_value] * FullCell.energy / CellFormat.housing_weight 

                # %if it's a "Teil"
                elif prices.loc[prices['Name'] == key.loc[i+1, "Name"], "Einheit"] == "€/Teil":
                    # %            prices(key{i, "Name"}, inputValue);
                    vals[i+1] = prices.loc[prices['Name'] == key.loc[i+1, "Name"], input_value]
                    # msgbox(append("Einheit von ", prices(key(i+1, "Name"), "Name"), "  == €/Teil")) # message box?

                elif prices.loc[prices['Name'] == key.loc[i+1, "Name"], "Einheit"] == "€/kg":
                    # %if so: get price and update value 
                    prices.loc[prices['Name'] == key.loc[i+1, "Name"], input_value]
                    vals[i+1] = prices.loc[prices['Name'] == key.loc[i+1, "Name"], input_value]
                else:
                    # %prices(key{i, "Name"}, inputValue);
                    vals[i+1] = prices.loc[prices['Name'] == key.loc[i+1, "Name"], input_value]
                    # %msgbox(append("Einheit von ", prices{key{i, "Name"}, "Name"}, "  == ?"))

            # % get sum() for Anode and Kathode Cells
            if "Anode" == key.loc[i, "Name"]:
                anode_check     = 1
                cathode_check   = 0
                vals[i+1]       = 0
                anode_index     = i

            elif "Cathode" == key.loc[i, "Name"]:
                anode_check     = 0
                cathode_check   = 1
                vals[i+1]      = 0
                cathode_index   = i

            elif key.loc[i, "Name"] == "Seperator" or key.loc[i, "Name"] == "Electrolyte" or key.loc[i, "Name"] == "Tabs":
                anode_check = 0
                cathode_check = 0
            
            # % increase the x_sum (Anode, Cathode)
            if anode_check == 1:
                anode_sum = anode_sum + vals[i+1]
            elif cathode_check == 1:
                cathode_sum = cathode_sum + vals[i+1]

        # % insert column into  "Price / €/kg" dataframe
        bom_table["Price / €/kg"] = vals