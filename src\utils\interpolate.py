from typing import Optional
import pandas as pd
import numpy as np

def interpolate(values: pd.Series, x: pd.Series, y: pd.Series, default: Optional[float] = None):
    if x.empty:
        if isinstance(values, pd.Series):
            result = values.copy()
            result.loc[:] = default
            return result
        else:
            return [default for v in values]

    if default is None:
        left = x.iloc[0]
        right = x.iloc[-1]
    else:
        left = default
        right = default

    if x.is_monotonic_decreasing:
        x = -x
        (left, right) = (right, left)
        if isinstance(values, pd.Series):
            values = -values
        else:
            values = [-v for v in values]
    
    return np.interp(values, x, y, left = default, right = default)
