db["cell-designs"].aggregate([
    {
        "$addFields": {
            "anodeBinderMaterials": [
                {
                    "materialId": "binder_paa_aquacharge",
                    "weightPercent": "$binderMaterialAnodeWeightPercent"
                }
            ],
            "anodeConductiveAdditiveMaterials": [
                {
                    "materialId": "leitruss_c65",
                    "weightPercent": "$leitrussAnodeWeightPercent"
                },
                {
                    "materialId": "cnt",
                    "weightPercent": "$cntAnodeWeightPercent"
                }
            ],
            "cathodeBinderMaterials": [
                {
                    "materialId": "binder_5130_solef",
                    "weightPercent": "$binderMaterialCathodeWeightPercent"
                }
            ],
            "cathodeConductiveAdditiveMaterials": [
                {
                    "materialId": "leitruss_c65",
                    "weightPercent": "$leitrussCathodeWeightPercent"
                },
                {
                    "materialId": "cnt",
                    "weightPercent": "$cntCathodeWeightPercent"
                }
            ],
            "electrolyteMaterialId": "default_electrolyte",
            "separatorMaterialId": "separator",
            "aluminiumCurrentCollectorMaterialId": "aluminium",
            "copperCurrentCollectorMaterialId": "copper"
        }
    },
    {
        "$project": {
            "binderMaterialAnodeWeightPercent": 0,
            "leitrussAnodeWeightPercent": 0,
            "cntAnodeWeightPercent": 0,
            "binderMaterialCathodeWeightPercent": 0,
            "leitrussCathodeWeightPercent": 0,
            "cntCathodeWeightPercent": 0
        }
    },
    {
        "$out": "cell-designs"
    }
]);
