from flask import Blueprint
import jsons
from auth import bearer
from models.business_case_model import BusinessCaseModel
from utils.camelcase_to_snakecase import snakecase_to_camelcase
from jsons_config import jsons_flask_fork

business_case_blueprint = Blueprint('business_case_api', __name__, url_prefix='/api/business-case')

@business_case_blueprint.get('/')
@bearer.secure('user')
def get_all_business_cases() -> list[BusinessCaseModel]:
    business_cases = [
        BusinessCaseModel(_id = "100 MWh", label = "100 MWh"),
        BusinessCaseModel(_id = "1.3 GWh", label = "1.3 GWh"),
        BusinessCaseModel(_id = "10 GWh", label = "10 GWh"),
        BusinessCaseModel(_id = "20 GWh", label = "20 GWh"),
        BusinessCaseModel(_id = "30 GWh", label = "30 GWh"),
        BusinessCaseModel(_id = "B2/B3", label = "B2/B3")
    ]

    return [jsons.dump(model, cls = BusinessCaseModel, fork_inst = jsons_flask_fork, strict = True, key_transformer = snakecase_to_camelcase) for model in business_cases]
