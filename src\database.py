from typing import Optional
import boto3
from botocore.exceptions import ClientError
import os
from pymongo import MongoClient
from urllib.parse import quote_plus

fqdn_option = os.environ.get("MONGODB_FQDN", "localhost")
port_option = os.environ.get("MONGODB_PORT", "27017")
database_option = os.environ.get("MONGODB_DATABASE", "cell-o-mat")
username_option = os.environ.get("MONGODB_USERNAME")
password_option = os.environ.get("MONGODB_PASSWORD")
password_secret_arn_option = os.environ.get("SECRETSMANAGER_MONGODB_PASSWORD_ARN")
tls_ca_file_option = os.environ.get("MONGODB_TLS_CA_FILE")
documentdb_mode = os.environ.get("MONGODB_DOCUMENTDB_MODE") is not None

if password_option is None:
    if password_secret_arn_option is not None:
        # Create a Secrets Manager client
        region_name = "eu-central-1"
        client = boto3.client(
            service_name = "secretsmanager",
            region_name = region_name
        )
        try:
            get_secret_value_response = client.get_secret_value(
                SecretId=password_secret_arn_option
                )
        except ClientError as e:
            # For a list of exceptions thrown, see
            # https://docs.aws.amazon.com/secretsmanager/latest/apireference/API_GetSecretValue.html
            raise e

        # Decrypts secret using the associated KMS key.
        password_option = get_secret_value_response['SecretString']

def build_uri(fqdn: str, port: Optional[str], database: Optional[str], username: Optional[str], password: Optional[str], tls_ca_file: Optional[str], documentdb_mode: bool) -> str:
    credentials = (f"{quote_plus(username)}:{quote_plus(password)}" if password is not None else quote_plus(username)) if username is not None else None

    uri = fqdn
    uri = f"{credentials}@{uri}" if credentials is not None else uri
    uri = f"{uri}:{port}" if port is not None else uri
    uri = f"{uri}/{database}" if database is not None else uri
    uri = f"mongodb://{uri}"

    options = []
    if tls_ca_file is not None:
        options.append(f"tls=true")
        options.append(f"tlsCAFile={quote_plus(tls_ca_file)}")

    if documentdb_mode:
        options.append("replicaSet=rs0")
        options.append("readPreference=secondaryPreferred")
        options.append("retryWrites=false")

    if len(options) > 0:
        query = "&".join(options)
        uri = f"{uri}?{query}"

    return uri

uri = build_uri(
    fqdn = fqdn_option,
    port = port_option,
    database = database_option,
    username = username_option,
    password = password_option,
    tls_ca_file = tls_ca_file_option,
    documentdb_mode = documentdb_mode
)

redacted_uri = build_uri(
    fqdn = fqdn_option,
    port = port_option,
    database = database_option,
    username = username_option,
    password = '<REDACTED>' if password_option is not None else None,
    tls_ca_file = tls_ca_file_option,
    documentdb_mode = documentdb_mode
)

print(redacted_uri)

client = MongoClient(uri, connect=False, tz_aware=True)
database = client.get_default_database()
