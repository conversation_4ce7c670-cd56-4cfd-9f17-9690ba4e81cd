from auth import bearer
from flask import Blueprint
from electrochemistry.active_material import Material

from electrochemistry.material_collection import material_collection

inactive_material_blueprint = Blueprint('inactive_material_api', __name__, url_prefix='/api/inactive-material')

inactive_materials = material_collection.get_inactive_materials()

def _inactive_material_to_dict(inactive_material: Material) -> dict:
    return {
        'id': inactive_material.id,
        'name': inactive_material.name,
        'type': str(inactive_material.type),
        'density': inactive_material.density,
        'isAnode': inactive_material.is_anode,
        'isCathode': inactive_material.is_cathode
    }

@inactive_material_blueprint.get('/')
@bearer.secure('user')
def get_all_materials():
    return [_inactive_material_to_dict(m) for m in inactive_materials]

@inactive_material_blueprint.get('/<string:material_id>')
@bearer.secure('user')
def get_single_material(material_id: str):
    return next(_inactive_material_to_dict(m) for m in inactive_materials if m.id == material_id)
