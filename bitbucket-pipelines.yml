# Further attributes can be found here
# https://bitbucket.org/atlassian/aws-lambda-deploy/src/master/
pipelines:
  # Pipeline for main branch
  branches:
    prod:
      - step:
          name: Build & Push image (prod)
          oidc: true
          script:
            - export APP_VERSION=$(cat ./VERSION).$(printf '%(%Y%m%d)T\n' -1)-$(git rev-parse HEAD)
            # build the image
            - DOCKER_BUILDKIT=1 docker build -t cellomat2-global-backend:$APP_VERSION -t cellomat2-global-backend:latest -t cellomat2-global-backend:prod .
            - pipe: atlassian/aws-ecr-push-image:1.6.2
              variables:
                AWS_DEFAULT_REGION: "eu-central-1"
                AWS_OIDC_ROLE_ARN: "arn:aws:iam::014923895365:role/cellomat2-global-backend-bitbucket-ci-cd-role"
                IMAGE_NAME: "cellomat2-global-backend"
                TAGS: "$APP_VERSION latest prod"
      - step:
          name: Deploy image to ECS
          deployment: Production
          oidc: true
          image: amazon/aws-cli
          script:
            - yum install -y jq
            - export AWS_REGION=eu-central-1
            - export AWS_ROLE_ARN=arn:aws:iam::014923895365:role/cellomat2-global-backend-bitbucket-ci-cd-role
            - export AWS_WEB_IDENTITY_TOKEN_FILE=$(pwd)/web-identity-token
            - echo $BITBUCKET_STEP_OIDC_TOKEN > $(pwd)/web-identity-token
            - echo "#########################################"
            - echo "# Deploy to $ENV ECS Cluster"
            - echo "#########################################"
            - aws ecs update-service --cluster cellomat2-$ENV-cluster --service cellomat2-$ENV-backend --force-new-deployment
            - aws ecs wait services-stable --cluster cellomat2-$ENV-cluster --services cellomat2-$ENV-backend
    main:
      - step:
          name: Build & Push image (dev)
          oidc: true
          script:
            - export APP_VERSION=$(cat ./VERSION).$(printf '%(%Y%m%d)T\n' -1)-$(git rev-parse HEAD)
            # build the image
            - DOCKER_BUILDKIT=1 docker build -t cellomat2-global-backend:$APP_VERSION -t cellomat2-global-backend:latest -t cellomat2-global-backend:dev .
            - pipe: atlassian/aws-ecr-push-image:1.6.2
              variables:
                AWS_DEFAULT_REGION: "eu-central-1"
                AWS_OIDC_ROLE_ARN: "arn:aws:iam::014923895365:role/cellomat2-global-backend-bitbucket-ci-cd-role"
                IMAGE_NAME: "cellomat2-global-backend"
                TAGS: "$APP_VERSION latest dev"
      - step:
          name: Deploy image to ECS
          deployment: Development
          oidc: true
          image: amazon/aws-cli
          script:
            - yum install -y jq
            - export AWS_REGION=eu-central-1
            - export AWS_ROLE_ARN=arn:aws:iam::014923895365:role/cellomat2-global-backend-bitbucket-ci-cd-role
            - export AWS_WEB_IDENTITY_TOKEN_FILE=$(pwd)/web-identity-token
            - echo $BITBUCKET_STEP_OIDC_TOKEN > $(pwd)/web-identity-token
            - echo "#########################################"
            - echo "# Deploy to $ENV ECS Cluster"
            - echo "#########################################"
            - aws ecs update-service --cluster cellomat2-$ENV-cluster --service cellomat2-$ENV-backend --force-new-deployment
            - aws ecs wait services-stable --cluster cellomat2-$ENV-cluster --services cellomat2-$ENV-backend
