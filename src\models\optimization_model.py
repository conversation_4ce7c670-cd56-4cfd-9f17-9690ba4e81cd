from bson import ObjectId
from dataclasses import dataclass
import datetime
from typing import Literal, Optional

from models.chart_model import ChartDatasetModel
from models.optimization_settings_model import OptimizationSettingParameter, OptimizationSettingVariable
from models.user_model import UserModel

OptimizationStatus = Literal['pending', 'complete', 'error']

@dataclass(kw_only = True)
class OptimizationChart():
    datasets: list[ChartDatasetModel]

@dataclass(kw_only = True)
class OptimizationVariable(OptimizationSettingVariable):
    min_value: float
    max_value: float
    step_size: Optional[float] = None
    optimal: Optional[float] = None
    original: Optional[float] = None

@dataclass(kw_only=True)
class OptimizationOutcome:
    id: Optional[str] = None
    name: Optional[str] = None
    original_value: Optional[float] = None
    optimized_value: Optional[float] = None
    unit:Optional[str] = None

@dataclass(kw_only = True)
class OptimizationModel:
    algorithm: OptimizationSettingParameter  # id: AlgorithmType
    stop_criterion_type: Optional[OptimizationSettingParameter] = None  # id: StopCriterionType
    stop_criterion_value: Optional[int] = None
    variables: list[OptimizationVariable]
    population_size: Optional[int] = None
    objective: OptimizationSettingParameter
    chart: Optional[OptimizationChart] = None
    status: Optional[str] = None  # OptimizationStatus
    num_initial_points: Optional[int] = None
    num_iterations: Optional[int] = None
    optimization_outcome: Optional[list[OptimizationOutcome]] = None

@dataclass(kw_only = True)
class OptimizationModelGenerated(OptimizationModel):
    version: str = '1.0.0'
    created_at: datetime.datetime
    created_by: UserModel
    modified_at: datetime.datetime
    modified_by: UserModel
    deleted_at: Optional[datetime.datetime]
    deleted_by: Optional[UserModel]

@dataclass(kw_only = True)
class OptimizationModelWithId(OptimizationModelGenerated):
    _id: ObjectId
