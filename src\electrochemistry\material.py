
from dataclasses import dataclass
from enum import Enum
from typing import Optional

class MaterialType(Enum):
    COMPOSITE = 1
    ACTIVE_MATERIAL = 2
    LITHIUM = 3
    BINDER = 4
    CONDUCTIVE_ADDITIVE = 5
    ELECTROLYTE = 6
    CURRENT_COLLECTOR = 7
    SEPARATOR = 8
    HOUSING = 9
    TAB = 10
    ELECTRODE = 11
@dataclass
class Material(object):

    id: str
    name: str
    density: float
    type: MaterialType
    theoretical_capacity: Optional[float]
    specific_resistance: Optional[float]
    sei_loss_ah: Optional[float]
    is_anode: bool
    is_cathode: bool

    active_surface: Optional[float]

    type: MaterialType

    def __init__(self, id: str, name: str, density: float, type: MaterialType, theoretical_capacity: Optional[float] = None, specific_resistance: Optional[float] = None, active_surface: Optional[float] = None, sei_loss_ah: Optional[float] = None, is_anode: bool = False, is_cathode: bool = False):
        self.id = id
        self.name = name
        self.density = density
        self.type = type
        self.theoretical_capacity = theoretical_capacity
        self.specific_resistance = specific_resistance
        self.active_surface = active_surface
        self.sei_loss_ah = sei_loss_ah
        self.is_anode = is_anode
        self.is_cathode = is_cathode

@dataclass
class MaterialWeight:
    material: Material
    weight: float

lithium = Material(id = 9001, name="Lithium", density = 0.534, type=MaterialType.LITHIUM, theoretical_capacity = 1 / 6.914 * 96485 * 1 / 3.6)
li_cl = Material(id = 9002, name="LiCl", density=0.534, type=MaterialType.LITHIUM, theoretical_capacity = 1 / 6.914 * 96485 * 1 / 3.6)
leitruss_c65 = Material(id = 9003, name = "Leitruss C65", density = 1.6, type = MaterialType.CONDUCTIVE_ADDITIVE, theoretical_capacity = None)
cnt = Material(id = 9004, name = "Carbon Nanotubes", density = 1.6, type = MaterialType.CONDUCTIVE_ADDITIVE, theoretical_capacity = None)
binder_5130_solef = Material(id = 9005, name = "Binder 5130 Solef", density = 1.77, type = MaterialType.BINDER, theoretical_capacity = None)
binder_paa_aquacharge = Material(id = 9006, name = "Binder PAA Aquacharge", density = 1.1, type = MaterialType.BINDER, theoretical_capacity = None)
