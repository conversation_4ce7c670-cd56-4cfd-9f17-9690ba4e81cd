import argparse
import json
import jsons
from jsons_config import jsons_mongodb_fork
from pymongo import ReturnDocument

from database import database
from models.cell_design_model import CellDesignModelGenerated, CellDesignModelWithId
from models.user_model import UserModel, UserModelWithId
from utils.camelcase_to_snakecase import camelcase_to_snakecase, snakecase_to_camelcase

def upsert_user(user: UserModel) -> UserModelWithId:
    document = database['users'].find_one_and_update(
        { 'oid': user.oid },
        { '$set': { 'oid': user.oid, 'email': user.email, 'name': user.name } },
        upsert = True,
        return_document = ReturnDocument.AFTER
    )

    return jsons.load(document, UserModelWithId, key_transformer = camelcase_to_snakecase)

def model_to_document(model: CellDesignModelGenerated) -> object:
    document = jsons.dump(model, CellDesignModelGenerated, fork_inst = jsons_mongodb_fork, key_transformer = snakecase_to_camelcase, transform_dict_keys = False)
    document['createdBy'] = upsert_user(model.created_by)._id
    document['modifiedBy'] = upsert_user(model.modified_by)._id
    document['deletedBy'] = upsert_user(model.deleted_by)._id if model.deleted_by is not None else None
    document['releasedBy'] = upsert_user(model.released_by)._id if model.released_by is not None else None
    return document

def run(filename: str) -> int:
    # load JSON file
    with open(filename, "r") as file:
        models = json.load(file)

    models = [jsons.load(model, CellDesignModelGenerated, fork_inst = jsons_mongodb_fork, strip = True, key_transformer = camelcase_to_snakecase, transform_dict_keys = False) for model in models]
    documents = [model_to_document(model) for model in models]

    # upsert designs
    for document in documents:
        database['cell-designs'].find_one_and_update(
            { 'designId': document['designId'] },
            { '$set': document },
            upsert = True
        )

    return 0

if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        prog = 'COM2 DB Ingest',
        description = 'Ingests old MAT designs into the new database.'
    )

    parser.add_argument('filename')
    args = parser.parse_args()
    kwargs = { key: value for (key, value) in args._get_kwargs() }
    code = run(*args._get_args(), **kwargs)
    exit(code)
