from flask import Blueprint
import jsons
from auth import bearer
from models.cell_format_model import CellFormatModel
from electrochemistry.serialization import default_format_definitions
from jsons_config import jsons_flask_fork
from utils.camelcase_to_snakecase import snakecase_to_camelcase

cell_format_blueprint = Blueprint('cell_format_api', __name__, url_prefix='/api/cell-format')

default_format_models = {id: CellFormatModel(id = definition.id, name = definition.name, geometry = definition.geometry, default_properties = definition.default_properties) for id, definition in default_format_definitions.items()}

@cell_format_blueprint.get('/')
@bearer.secure()
def get_all_cell_formats() -> list[CellFormatModel]:
    '''
    Gets a list of all cell formats available for selection in the frontend.
    Request Body: none
    Reponse Body: list of the cell formats
    Status Code: 200 OK
    '''
    return jsons.dump([default_format_models[key] for key in default_format_models.keys()], fork_inst = jsons_flask_fork, key_transformer = snakecase_to_camelcase)

@cell_format_blueprint.get('/<string:cell_format_id>')
@bearer.secure()
def get_single_cell_format(cell_format_id: str) -> CellFormatModel:
    '''
    Gets a single cell format referenced by id.
    Route Parameter cell_format_id: The 
    Request Body: none
    Reponse Body: list of the cell formats
    Status Code: 200 OK if id exists, 404 NOT FOUND otherwise.
    '''
    cell_format = default_format_models.get(cell_format_id)
    if cell_format is None:
        return 'NOT FOUND', 404
    else:
        return jsons.dump(cell_format, CellFormatModel, fork_inst = jsons_flask_fork, key_transformer = snakecase_to_camelcase)
