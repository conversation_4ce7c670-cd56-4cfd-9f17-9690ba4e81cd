from abc import abstractmethod
import math
from typing import Optional, Union
from electrochemistry.cell_layer import CellLayer
from electrochemistry.material import Material
from electrochemistry.serialization import CellFormatCylinderProperties, CellFormatPouchProperties, CellFormatPrismaProperties, CellGeometry
from electrochemistry.component import Component
from electrochemistry.electrolyte import Electrolyte

class CellFormat:
    id: str
    properties: Union[CellFormatPouchProperties, CellFormatPrismaProperties, CellFormatCylinderProperties]
    cathode_tab: Component
    anode_tab: Component
    housing: Material
    cell_layer: CellLayer
    electrolyte: Electrolyte

    _user_set_housing_weight: Optional[float]

    housing_thickness: float
    assembly_clearance: float

    geometry: CellGeometry
    cathode_coating_length: float
    anode_coating_length: float
    separator_coating_length: float
    electrolyte_swelling: float
    max_cell_layer_thickness: float
    overlap_thickness: float
    no_cell_layers: float
    no_anode_layers: float
    no_cathode_layers: float
    no_separator_layers: float
    cell_layer_thickness: float
    separator_area_total: float

    def __init__(self, id:str, properties: Union[CellFormatPouchProperties, CellFormatPrismaProperties, CellFormatCylinderProperties], cathode_tab: Component, anode_tab: Component, housing: Material, cell_layer: CellLayer, electrolyte: Electrolyte):
        self.id = id
        self.properties = properties
        self.cathode_tab = cathode_tab
        self.anode_tab = anode_tab
        self.housing = housing
        self.cell_layer = cell_layer
        self.electrolyte = electrolyte
        
        
        self._user_set_housing_weight = properties.housing_weight

        self.housing_thickness = properties.housing_thickness
        self.assembly_clearance = 0.0

    @property
    def housing_weight_with_tabs(self) -> float:
        return self.housing_weight + self.cathode_tab.weight + self.anode_tab.weight if self._user_set_housing_weight is None else self._user_set_housing_weight
    
    @property
    def cathode_tab_weight(self) -> float:
        return self.cathode_tab.weight if self._user_set_housing_weight is None else 0

    @property
    def anode_tab_weight(self) -> float:
        return self.anode_tab.weight if self._user_set_housing_weight is None else 0

    @property
    def cathode_coating_area(self) -> float:
        return self.properties.coating_width_cathode * self.cathode_coating_length

    @property
    def anode_coating_area(self) -> float:
        return self.properties.coating_width_anode * self.anode_coating_length

    @property
    def separator_area(self) -> float:
        return self.properties.coating_width_separator * self.separator_coating_length

    @property
    @abstractmethod
    def cathode_coating_length(self) -> float:
        pass

    @property
    @abstractmethod
    def anode_coating_length(self) -> float:
        pass

    @property
    @abstractmethod
    def separator_coating_length(self) -> float:
        pass

    @property
    @abstractmethod
    def volume(self) -> float:
        pass

    @property
    @abstractmethod
    def housing_weight(self) -> float:
        pass
    
    @abstractmethod
    def calculate_cell_layers(self, overwrite_cell_layers:int = None):
        pass

class CellFormatPouch(CellFormat):
    properties: CellFormatPouchProperties
    has_two_substacks: bool

    delta_cell_layer_thickness: float
    mod_cell_layer_thickness: float

    def __init__(self, id:str, properties: CellFormatPouchProperties, cathode_tab: Component, anode_tab: Component, housing: Material, cell_layer: CellLayer, electrolyte: Electrolyte, has_two_substacks: bool = False):
        self.has_two_substacks = has_two_substacks
        self.geometry = CellGeometry.POUCH
        super().__init__(id, properties, cathode_tab, anode_tab, housing, cell_layer, electrolyte)

    @property
    def cathode_coating_length(self) -> float:
        return self.properties.coating_length_cathode

    @property
    def anode_coating_length(self) -> float:
        return self.properties.coating_length_anode

    @property
    def separator_coating_length(self) -> float:
        return self.properties.coating_length_separator

    @property
    def volume(self) -> float:
        return self.properties.cell_length * self.properties.cell_width * self.properties.cell_thickness

    @property
    def housing_weight(self) -> float:
        if self.properties.housing_weight is None:
            return ((2 * self.properties.cell_length * self.properties.cell_width) + (2 * self.properties.cell_length * self.properties.cell_thickness) + (2 * self.properties.cell_width * self.properties.cell_thickness)) * self.housing_thickness * self.housing.density / 1e3
        else:
            return self.properties.housing_weight

    def calculate_cell_layers(self, overwrite_cell_layers:Optional[int] = None):
        self.electrolyte_swelling = (self.properties.cell_thickness - 2 * self.properties.housing_thickness) * self.electrolyte.swelling
        self.max_cell_layer_thickness = (self.properties.cell_thickness - 2 * self.properties.housing_thickness) - self.electrolyte_swelling - self.assembly_clearance - self.properties.swelling_buffer
        if self.has_two_substacks:
            self.max_cell_layer_thickness = self.max_cell_layer_thickness / 2

        self.overlap_thickness = (2 * self.cell_layer.anode.coating_thickness + self.cell_layer.cu_cc.thickness + 2 * self.cell_layer.separator.thickness) * 1e-3
        repeating_cell_layer_thickness = self.max_cell_layer_thickness - self.overlap_thickness
        
        if overwrite_cell_layers is not None:
            self.no_cell_layers = overwrite_cell_layers
        else:
            self.no_cell_layers = math.floor(repeating_cell_layer_thickness * 1e3 / self.cell_layer.get_thickness())
            self.no_cell_layers = self.no_cell_layers - (self.no_cell_layers % 2)
        
        self.no_anode_layers = (self.no_cell_layers / 2) + 1
        self.no_cathode_layers = self.no_anode_layers - 1
        self.no_separator_layers = self.no_anode_layers * 2
        self.cell_layer_thickness = self.no_cell_layers * self.cell_layer.get_thickness() * 1e-3 + self.overlap_thickness
        self.delta_cell_layer_thickness = (self.max_cell_layer_thickness - self.cell_layer_thickness) * 1e3
        self.mod_cell_layer_thickness = self.delta_cell_layer_thickness / (2 * self.cell_layer.get_thickness())

        if self.has_two_substacks:
            self.overlap_thickness = self.overlap_thickness * 2
            self.max_cell_layer_thickness = self.max_cell_layer_thickness * 2
            self.no_cell_layers = self.no_cell_layers * 2
            self.no_anode_layers = self.no_anode_layers * 2
            self.no_cathode_layers = self.no_cathode_layers * 2
            self.no_separator_layers = self.no_separator_layers * 2
            self.cell_layer_thickness = self.cell_layer_thickness * 2
            self.delta_cell_layer_thickness = self.delta_cell_layer_thickness * 2
            self.mod_cell_layer_thickness = self.mod_cell_layer_thickness * 2

        self.separator_area_total = self.no_separator_layers * self.separator_area / 1e6

class CellFormatPrisma(CellFormatPouch):
    properties: CellFormatPrismaProperties

    def __init__(self, id:str, properties: CellFormatPrismaProperties, cathode_tab: Component, anode_tab: Component, housing: Material, cell_layer: CellLayer, electrolyte: Electrolyte):
        super().__init__(id, properties, cathode_tab, anode_tab, housing, cell_layer, electrolyte, properties.has_two_substacks)

class CellFormatCylinder(CellFormat):
    properties: CellFormatCylinderProperties

    cathode_length: float
    anode_length: float
    separator_length: float
    cathode_windings: float
    anode_windings: float
    separator_windings: float
    separator_area_total: float

    def __init__(self, id:str, properties: CellFormatCylinderProperties, cathode_tab: Component, anode_tab: Component, housing: Material, cell_layer: CellLayer, electrolyte: Electrolyte):
        self.geometry = CellGeometry.CYLINDER
        super().__init__(id, properties, cathode_tab, anode_tab, housing, cell_layer, electrolyte)

    @property
    def cathode_coating_length(self) -> float:
        return self.properties.coating_length_cathode if self.properties.coating_length_cathode is not None else self.cathode_length - 2

    @property
    def anode_coating_length(self) -> float:
        return self.properties.coating_length_anode if self.properties.coating_length_anode is not None else self.anode_length - 2

    @property
    def separator_coating_length(self) -> float:
        return self.properties.coating_length_separator if self.properties.coating_length_separator is not None else self.anode_length

    @property
    def volume(self) -> float:
        return math.pi * math.pow((self.properties.cell_diameter / 2), 2) * self.properties.cell_height

    @property
    def housing_weight(self) -> float:
        if self.properties.housing_weight is None:
            return 2 * math.pi * self.properties.cell_diameter / 2 * self.properties.cell_height * self.housing_thickness * self.housing.density / 1e3
        else:
            return self.properties.housing_weight

    def calculate_cell_layers(self, overwrite_cell_layers:int = None):
        self.electrolyte_swelling = (self.properties.cell_diameter - 2 * self.properties.housing_thickness) * self.electrolyte.swelling
        self.max_cell_layer_thickness = (self.properties.cell_diameter - 2 * self.properties.housing_thickness) - self.electrolyte_swelling - self.assembly_clearance - self.properties.swelling_buffer
        self.overlap_thickness = (2 * self.cell_layer.anode.coating_thickness + self.cell_layer.cu_cc.thickness + 2 * self.cell_layer.separator.thickness) * 1e-3
        self.no_cell_layers = 2
        self.no_anode_layers = 1
        self.no_cathode_layers = 1
        self.no_separator_layers = 2

        a = (2 * self.cell_layer.get_thickness()) * 1e-3 / (2 * math.pi) # increment of archimedian spiral, r=a*theta, mm/rad
        a_last = (self.overlap_thickness) / (2 * math.pi)

        def length_fun(phi: float, a: float) -> float:
            return (a / 2) * (phi * math.sqrt(1 + math.pow(phi, 2)) + math.log(phi + math.sqrt(1 + math.pow(phi, 2))))

        repeating_cell_layer_thickness = self.max_cell_layer_thickness - self.overlap_thickness
        self.cell_layer_thickness = self.max_cell_layer_thickness
        theta_max_cath = repeating_cell_layer_thickness / (2 * a) # angle at maximum radius rad
        theta_min = self.properties.cell_core_diameter / (2 * a) # angle for inner mandrel rad
        theta_max_last = ((repeating_cell_layer_thickness / 2) + self.overlap_thickness) / a_last
        theta_min_last = repeating_cell_layer_thickness / (2 * a_last)

        self.cathode_length = length_fun(theta_max_cath, a) - length_fun(theta_min, a)
        self.anode_length = self.cathode_length + (length_fun(theta_max_last, a_last) - length_fun(theta_min_last, a_last))
        self.separator_length = self.anode_length


        # Translate Anode overlap to angle: Assumption: overhang is following a circle instead of a spiral
        theta_overlap = (2 * math.pi * self.properties.anode_overhang) / (math.pi * repeating_cell_layer_thickness + self.overlap_thickness);
        # Calculate Windings
        self.cathode_windings = (theta_max_cath - theta_min) / (2 * math.pi)
        self.anode_windings = self.cathode_windings + (theta_max_last - theta_min_last + theta_overlap) / (2 * math.pi)
        self.separator_windings = self.anode_windings
        self.separator_area_total = self.no_separator_layers * self.separator_area / 1e6
