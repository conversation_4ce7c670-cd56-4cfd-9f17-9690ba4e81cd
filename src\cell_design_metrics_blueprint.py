from auth import bearer
from flask import Blueprint, request
import jsons
from electrochemistry.cell_format import CellFormatCylinder, CellFormatPouch, CellFormatPrisma
from electrochemistry.factory import create_full_cell
from electrochemistry.full_cell import FullCell
from electrochemistry.serialization import CellGeometry
from electrochemistry.full_cell_swelling import FullCellSwelling
from models.cell_design_model import CellDesignModel
from models.cell_design_metrics_model import *
from utils.camelcase_to_snakecase import camelcase_to_snakecase
from utils.exception_to_none import exception_to_none
from jsons_config import jsons_flask_fork

cell_design_metrics_blueprint = Blueprint('calculation_blueprint', __name__, url_prefix='/api/cell-design-metrics')

@cell_design_metrics_blueprint.post('/')
@bearer.secure('user')
def calculate_metrics():
    body = request.get_json()
    cell_design = jsons.load(body, CellDesignModel, key_transformer=camelcase_to_snakecase)
    metrics = calculate(cell_design)
    return jsons.dump(metrics, CellDesignMetricsModel, fork_inst = jsons_flask_fork, key_transformer=jsons.KEY_TRANSFORMER_CAMELCASE)

def _get_cell_design_metrics_cell_model(full_cell: FullCell) -> CellDesignMetricsCellModel:
    if full_cell.cell_format.geometry == CellGeometry.POUCH:
        cell_format_pouch: CellFormatPouch = full_cell.cell_format
        return CellDesignMetricsPouchCellModel(
            cell_volume = cell_format_pouch.volume / 1e3,
            housing_weight = cell_format_pouch.housing_weight_with_tabs,
            cathode_coating_area = cell_format_pouch.cathode_coating_area,
            anode_coating_area = cell_format_pouch.anode_coating_area,
            separator_coating_area = cell_format_pouch.separator_area,
            electrolyte_swelling = cell_format_pouch.electrolyte_swelling,
            assembly_clearance = cell_format_pouch.assembly_clearance,
            active_layer_count = cell_format_pouch.no_cell_layers,
            capacity_per_layer_c10 = full_cell.capacity_c10 / cell_format_pouch.no_cathode_layers,
            energy_per_layer_c10 = full_cell.energy_c10 / cell_format_pouch.no_cathode_layers,
            capacity_per_layer_c3 = full_cell.capacity_c3 / cell_format_pouch.no_cathode_layers if full_cell.capacity_c3 is not None else None,
            energy_per_layer_c3 = full_cell.energy_c3 / cell_format_pouch.no_cathode_layers if full_cell.energy_c3 is not None else None,
            separator_area_total = cell_format_pouch.separator_area_total,

            cell_layer_thickness_max = cell_format_pouch.max_cell_layer_thickness,
            cell_layer_thickness_total = cell_format_pouch.cell_layer_thickness,
            cell_layer_delta = cell_format_pouch.delta_cell_layer_thickness,
            ratio_cell_layer_delta_thickness = cell_format_pouch.mod_cell_layer_thickness,
            cathode_layer_count = cell_format_pouch.no_cathode_layers,
            anode_layer_count = cell_format_pouch.no_anode_layers,
            separator_layer_count = cell_format_pouch.no_separator_layers
        )
    elif full_cell.cell_format.geometry == CellGeometry.PRISMA:
        cell_format_prisma: CellFormatPrisma = full_cell.cell_format
        return CellDesignMetricsPrismaCellModel(
            cell_volume = cell_format_prisma.volume / 1e3,
            housing_weight = cell_format_prisma.housing_weight_with_tabs,
            cathode_coating_area = cell_format_prisma.cathode_coating_area,
            anode_coating_area = cell_format_prisma.anode_coating_area,
            separator_coating_area = cell_format_prisma.separator_area,
            electrolyte_swelling = cell_format_prisma.electrolyte_swelling,
            assembly_clearance = cell_format_prisma.assembly_clearance,
            active_layer_count = cell_format_prisma.no_cell_layers,
            capacity_per_layer_c10 = full_cell.capacity_c10 / cell_format_prisma.no_cathode_layers,
            energy_per_layer_c10 = full_cell.energy_c10 / cell_format_prisma.no_cathode_layers,
            capacity_per_layer_c3 = full_cell.capacity_c3 / cell_format_prisma.no_cathode_layers if full_cell.capacity_c3 is not None else None,
            energy_per_layer_c3 = full_cell.energy_c3 / cell_format_prisma.no_cathode_layers if full_cell.energy_c3 is not None else None,
            separator_area_total = cell_format_prisma.separator_area_total,

            cell_layer_thickness_max = cell_format_prisma.max_cell_layer_thickness,
            cell_layer_thickness_total = cell_format_prisma.cell_layer_thickness,
            cell_layer_delta = cell_format_prisma.delta_cell_layer_thickness,
            ratio_cell_layer_delta_thickness = cell_format_prisma.mod_cell_layer_thickness,
            cathode_layer_count = cell_format_prisma.no_cathode_layers,
            anode_layer_count = cell_format_prisma.no_anode_layers,
            separator_layer_count = cell_format_prisma.no_separator_layers
        )
    elif full_cell.cell_format.geometry == CellGeometry.CYLINDER:
        cell_format_cylinder: CellFormatCylinder = full_cell.cell_format
        return CellDesignMetricsCylinderCellModel(
            cell_volume = cell_format_cylinder.volume / 1e3,
            housing_weight = cell_format_cylinder.housing_weight_with_tabs,
            cathode_coating_area = cell_format_cylinder.cathode_coating_area,
            anode_coating_area = cell_format_cylinder.anode_coating_area,
            separator_coating_area = cell_format_cylinder.separator_area,
            electrolyte_swelling = cell_format_cylinder.electrolyte_swelling,
            assembly_clearance = cell_format_cylinder.assembly_clearance,
            active_layer_count = cell_format_cylinder.no_cell_layers,
            capacity_per_layer_c10 = full_cell.capacity_c10 / cell_format_cylinder.no_cathode_layers,
            energy_per_layer_c10 = full_cell.energy_c10 / cell_format_cylinder.no_cathode_layers,
            capacity_per_layer_c3 = full_cell.capacity_c3 / cell_format_cylinder.no_cathode_layers if full_cell.capacity_c3 is not None else None,
            energy_per_layer_c3 = full_cell.energy_c3 / cell_format_cylinder.no_cathode_layers if full_cell.energy_c3 is not None else None,
            separator_area_total = cell_format_cylinder.separator_area_total,

            cathode_coating_length = cell_format_cylinder.cathode_coating_length,
            anode_coating_length = cell_format_cylinder.anode_coating_length,
            separator_coating_length = cell_format_cylinder.separator_coating_length,
            cell_layer_diameter_max = cell_format_cylinder.max_cell_layer_thickness,
            cell_layer_diameter_total = cell_format_cylinder.cell_layer_thickness,
            cathode_length_total = cell_format_cylinder.cathode_length,
            anode_length_total = cell_format_cylinder.anode_length,
            cathode_winding_count = cell_format_cylinder.cathode_windings,
            anode_winding_count = cell_format_cylinder.anode_windings,
            separator_winding_count = cell_format_cylinder.separator_windings
        )

def calculate(design: CellDesignModel) -> CellDesignMetricsModel:

    full_cell = create_full_cell(design)
    cell_count = design.parallel_cells_count * design.serial_cells_count

    suggested_buffer = (full_cell.electrolyte.material.sei_loss_ah - full_cell.sei.growth_per_charge) * ((1.0 / full_cell.balancing.cell_formations.get_coulomb_efficiency()) - 1)
    return CellDesignMetricsModel(
        cell_swelling =  full_cell.swelling.get_swelling_model(),
        cathode = CellDesignMetricsElectrodeModel(
            summary = full_cell.cell_layer.cathode.active_material.get_summary_table((design.cathode_fixed_u_range or [None, None])[0], (design.cathode_fixed_u_range or [None, None])[1]),
            chart = full_cell.cell_layer.cathode.active_material.get_chart_data(500)
            ),
        anode = CellDesignMetricsElectrodeModel(
            summary = full_cell.cell_layer.anode.active_material.get_summary_table((design.anode_fixed_u_range or [None, None])[0], (design.anode_fixed_u_range or [None, None])[1]),
            chart = full_cell.cell_layer.anode.active_material.get_chart_data(500)
            ),
        balancing = CellDesignMetricsBalancingModel(
            summary = full_cell.balancing.get_summary_table(),
            chart = full_cell.balancing.get_chart_data(500),
            chart_annotations = full_cell.balancing.get_chart_annotations(),
            np_ratio_rev = full_cell.balancing.get_np_ratio_rev(),
            hysteresis = full_cell.balancing.hysteresis * 1000 if full_cell.balancing.hysteresis is not None else None,
            u_min = full_cell.balancing.u_min,
            u_max = full_cell.balancing.u_max,
            warning = full_cell.balancing.warning
        ),
        materials = CellDesignMetricsMaterialsModel(
            anode = CellDesignMetricsMaterialsElectrodeModel(
                active_material_density = full_cell.cell_layer.anode.active_material.composite_density,
                material1_weight_percent = full_cell.cell_layer.anode.active_material.materials[0].weight * full_cell.cell_layer.anode.get_active_material_weight_without_lithium() * 100,
                material1_density = full_cell.cell_layer.anode.active_material.materials[0].material.density,
                material2_weight_percent = full_cell.cell_layer.anode.active_material.materials[1].weight * full_cell.cell_layer.anode.get_active_material_weight_without_lithium() * 100 if len(full_cell.cell_layer.anode.active_material.materials) > 1 else None,
                material2_density = full_cell.cell_layer.anode.active_material.materials[1].material.density if len(full_cell.cell_layer.anode.active_material.materials) > 1 else None,
                binder_materials = [MaterialMetricsModel(material_id = m.material.id, weight_percent = m.weight * 100, density = m.material.density) for m in full_cell.cell_layer.anode.binder_materials],
                conductive_additive_materials = [MaterialMetricsModel(material_id = m.material.id, weight_percent = m.weight * 100, density = m.material.density) for m in full_cell.cell_layer.anode.conductive_additive_materials],
                total_density = full_cell.cell_layer.anode.get_composite_density(),
                full_cell_qrev = full_cell.balancing.cell_formations.get_formation_second_charge().capacity / full_cell.balancing.get_np_mass_ratio(), # app.CellBalance.CyclingData{4,2}/app.CellBalance.NPMassRatio,
                half_cell_qrev = full_cell.cell_layer.anode.active_material.formations.get_formation_second_charge().capacity, # app.AnodeBlend.CyclingData{4,2}
            ),
            prelithiation = CellDesignMetricsMaterialsPrelithiationModel(
                lithium_weight_percent = full_cell.cell_layer.anode.lithium_weight * 100,
                lithium_density = full_cell.cell_layer.anode.lithium_material.density,
                active_material_weight_percent = full_cell.cell_layer.anode.active_material_weight * 100,
                material1_weight_percent = full_cell.cell_layer.anode.active_material.materials[0].weight * full_cell.cell_layer.anode.active_material_weight * 100,
                material2_weight_percent = full_cell.cell_layer.anode.active_material.materials[1].weight * full_cell.cell_layer.anode.active_material_weight * 100 if len(full_cell.cell_layer.anode.active_material.materials) > 1 else None,
                binder_materials = [MaterialMetricsModel(material_id = m.material.id, weight_percent = m.weight * 100, density = m.material.density) for m in full_cell.cell_layer.anode.binder_materials],
                conductive_additive_materials = [MaterialMetricsModel(material_id = m.material.id, weight_percent = m.weight * 100, density = m.material.density) for m in full_cell.cell_layer.anode.conductive_additive_materials]
            ),
            cathode = CellDesignMetricsMaterialsElectrodeModel(
                active_material_density = full_cell.cell_layer.cathode.active_material.composite_density,
                material1_weight_percent = full_cell.cell_layer.cathode.active_material.materials[0].weight * full_cell.cell_layer.cathode.active_material_weight * 100,
                material1_density = full_cell.cell_layer.cathode.active_material.materials[0].material.density,
                material2_weight_percent = full_cell.cell_layer.cathode.active_material.materials[1].weight * full_cell.cell_layer.cathode.active_material_weight * 100 if len(full_cell.cell_layer.cathode.active_material.materials) > 1 else None,
                material2_density = full_cell.cell_layer.cathode.active_material.materials[1].material.density if len(full_cell.cell_layer.cathode.active_material.materials) > 1 else None,
                binder_materials = [MaterialMetricsModel(material_id = m.material.id, weight_percent = m.weight * 100, density = m.material.density) for m in full_cell.cell_layer.cathode.binder_materials],
                conductive_additive_materials = [MaterialMetricsModel(material_id = m.material.id, weight_percent = m.weight * 100, density = m.material.density) for m in full_cell.cell_layer.cathode.conductive_additive_materials],
                total_density = full_cell.cell_layer.cathode.get_composite_density(),
                full_cell_qrev = full_cell.balancing.cell_formations.get_formation_second_charge().capacity, # app.CellBalance.CyclingData{4,2},
                half_cell_qrev = full_cell.cell_layer.cathode.active_material.formations.get_formation_second_charge().capacity, # app.CathodeBlend.CyclingData{4,2}
            ),
            electrolyte_density = full_cell.electrolyte.material.density,
            separator_density = full_cell.cell_layer.separator.material.density,
            separator_porousness = full_cell.separator_porosity,
            aluminium_density = full_cell.cell_layer.al_cc.material.density,
            copper_density = full_cell.cell_layer.cu_cc.material.density
        ),
        electrode_pair = CellDesignMetricsElectrodePairModel(
            cathode_porosity = full_cell.cell_layer.cathode.get_porosity(),
            anode_porosity = full_cell.cell_layer.anode.get_porosity(),
            balancing = full_cell.balancing.get_np_ratio_rev(),
            anode_area_capacity = full_cell.cell_layer.anode.areal_capacity,
            cathode_loading = full_cell.cell_layer.cathode.loading,
            anode_loading = full_cell.cell_layer.anode.loading,
            cathode_coating_thickness = full_cell.cell_layer.cathode.coating_thickness,
            anode_coating_thickness = full_cell.cell_layer.anode.coating_thickness,
            cell_layer_thickness = full_cell.cell_layer.get_thickness(),
            cathode_thickness = full_cell.cell_layer.cathode.get_thickness(),
            anode_thickness = full_cell.cell_layer.anode.get_thickness(),
            design_datasets = [
                CellDesignMetricsElectrodePairDatasetModel(label = 'Kupfer', background_color = '#8D402A', data = [full_cell.cell_layer.cu_cc.thickness / 2]),
                CellDesignMetricsElectrodePairDatasetModel(label = 'Anode', background_color = '#333333', data = [full_cell.cell_layer.anode.coating_thickness]),
                CellDesignMetricsElectrodePairDatasetModel(label = 'Separator', background_color = '#D0BDA3', data = [full_cell.cell_layer.separator.thickness]),
                CellDesignMetricsElectrodePairDatasetModel(label = 'Kathode', background_color = '#2C4557', data = [full_cell.cell_layer.cathode.coating_thickness]),
                CellDesignMetricsElectrodePairDatasetModel(label = 'Aluminium', background_color = '#838688', data = [full_cell.cell_layer.al_cc.thickness / 2])
            ]
        ),
        cell = _get_cell_design_metrics_cell_model(full_cell),
        electrolyte = CellDesignMetricsElectrolyteModel(
            pore_volume_ah = full_cell.pore_volume_ah,
            pore_volume = full_cell.pore_volume,
            electrolyte_amount = full_cell.electrolyte.volume,
            electrolyte_amount_suggestion_ah = suggested_buffer + full_cell.pore_volume_ah,
            electrolyte_amount_suggestion = (suggested_buffer + full_cell.pore_volume_ah) * full_cell.capacity_c10,
            electrolyte_amount_sei = full_cell.electrolyte.material.sei_loss_ah,
            first_cycle_efficiency = full_cell.balancing.cell_formations.get_coulomb_efficiency() * 100,
            sei_growth_ml_ah = 0.61,
            sei_growth_nm_ah = full_cell.sei.growth_nm_per_charge,
            anode_active_surface = full_cell.sei.growth_per_charge / full_cell.sei.growth_nm_per_charge * 1e3,
            aging_table = full_cell.get_aging_table(design.aging_table_edit_values)
        ),
        summary = CellDesignMetricsSummaryModel(
            cell_weight_overall = full_cell.weight,
            cell_volume_overall = full_cell.volume,
            price_cell = full_cell.price,
            safe_nominal_voltage_c10 = full_cell.u_nom_c10,
            safe_cell_capacity_c10 = full_cell.safe_capacity_c10,
            safe_cell_energy_c10 = full_cell.safe_energy_c10,
            safe_cell_energy_density_volumetric_c10 = full_cell.safe_energy_density_l_c10,
            safe_cell_energy_density_gravimetric_c10 = full_cell.safe_energy_density_kg_c10,
            safe_cell_price_kwh_c10 = full_cell.safe_price_kwh_c10,
            safe_nominal_voltage_c3 = full_cell.u_nom_c3,
            safe_cell_capacity_c3 = full_cell.safe_capacity_c3,
            safe_cell_energy_c3 = full_cell.safe_energy_c3,
            safe_cell_energy_density_volumetric_c3 = full_cell.safe_energy_density_l_c3,
            safe_cell_energy_density_gravimetric_c3 = full_cell.safe_energy_density_kg_c3,
            safe_cell_price_kwh_c3 = full_cell.price_kwh_c3,
            cell_count = cell_count,
            pack_nominal_voltage = full_cell.u_nom * design.serial_cells_count,
            safe_pack_capacity = full_cell.safe_capacity * cell_count,
            safe_pack_energy = (full_cell.safe_energy * cell_count) / 1000,
            pack_weight = (full_cell.weight * cell_count) / 1000,
            pack_price = exception_to_none(lambda: full_cell.price * cell_count)
        ),
        bom = CellDesignMetricsBillOfMaterialModel(
            table = full_cell.get_bom_table()
        )
    )
