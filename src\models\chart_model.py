from typing import Literal, Optional, TypedDict

class BoxAnnotationLabel(TypedDict):
    display: bool
    content: str

class BoxAnnotation(TypedDict):
    label: BoxAnnotationLabel # Content is the name of the zone. Display is to is to whether display the name in the chart zone or not and should be set to flase for now as we dont want to display the name currently.
    id: str # should be unique for each zone
    type: Literal['box'] # we need this to be set to box
    x_min: Optional[float] # start of the zone on x axis
    x_max: Optional[float] # end of the zone on x axis
    draw_time: Literal['beforeDatasetsDraw'] # should be set to beforeDatasetsDraw so that everything else is drawn on top of the zone
    background_color: str # the color of the zone (might consider adding opacity in the color itself). color and label.content should be matched meaning a color should always be connected to one and the same name and name to one and the same color

class ChartPointModel(TypedDict):
    x: float
    y: float

class ChartDatasetModel(TypedDict):
    id: Optional[str]
    description: str # description of row in table. first entry per row is used.
    label: str # tooltip of the line
    data: list[ChartPointModel]
    hidden: Optional[bool]
    border_color: Optional[str]
    border_dash: Optional[list[float]]

class ChartModel(TypedDict):
    headers: list[str]
    datasets: list[ChartDatasetModel] # normalized list of all datasets in the table (number of headers determines the layout)
