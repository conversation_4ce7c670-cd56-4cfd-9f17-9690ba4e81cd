import re

def camelcase_to_snakecase(name: str) -> str:
    return name[0].lower() + re.sub(r'([A-Z])', '_\\1', name[1:]).lower()

def snakecase_to_camelcase(name: str) -> str:
    if name[0] == '_':
        # preserve leading underscores
        return '_' + snakecase_to_camelcase(name[1:])
    else:
        name = ''.join((part[0].upper() + part[1:]) if len(part) > 0 else '_' for part in name.split('_'))
        return name[0].lower() + name[1:]
