from auth import bearer
from bson import ObjectId
import datetime
from flask import Blueprint, request
import jsons
from pymongo.results import InsertOneResult
import threading
import traceback

from cell_design_blueprint import get_design, write_design
from database import client, database
from db_actions import update_user_table
from electrochemistry.optimization_mapping import optimization_variables, optimization_parameters,optimization_objectives
from electrochemistry.optimization import optimize, WeightDesignTemplate,full_cell_field_paths, Optimization
from jsons_config import jsons_flask_fork, jsons_mongodb_fork
from models.cell_design_model import CellDesignModel, CellDesignModelWithMetadata
from models.chart_model import ChartDatasetModel
from models.optimization_request_model import OptimizationRequestModel, OptimizationSettingsRequestModel
from models.optimization_model import OptimizationModel, OptimizationModelWithId, OptimizationChart,OptimizationOutcome
from settingslocal import VERSION
from utils.camelcase_to_snakecase import camelcase_to_snakecase, snakecase_to_camelcase
from electrochemistry.factory import create_full_cell
from electrochemistry.full_cell_swelling import FullCellSwelling
from copy import deepcopy

optimization_blueprint = Blueprint('optimization_blueprint', __name__, url_prefix='/api/optimization')

def _parse_parameter(document: dict, parameter: str) -> None:
    if document.get(parameter):
        document[parameter] = {
            'id': camelcase_to_snakecase(document[parameter]),
            'name': optimization_parameters.get(document[parameter], {'name': document[parameter]})['name'],
            'unit': optimization_parameters.get(document[parameter], {'unit': document[parameter]}).get('unit', "")
        }
    return document

def _map_names_in_document(document: dict) -> dict:
    for variable in document['variables']:
        variable.update(optimization_variables.get(variable['id'], {}))

    _parse_parameter(document, 'algorithm')
    _parse_parameter(document, 'stopCriterionType')
    _parse_parameter(document, 'objective')

    return document

def _generate_outcome(original:CellDesignModel, optimized: CellDesignModel):
    original_cell = create_full_cell(original)
    optimized_cell = create_full_cell(optimized)
    res = []
    res.extend(_create_optimization_outcome(full_cell_field_paths, original_cell, optimized_cell))
    return res

def _create_optimization_outcome(field_paths, original, optimized):
    res = []
    for optimization, field in field_paths.items():
        original_value = getattr(original, field)
        optimized_value = getattr(optimized, field)

        res.append(OptimizationOutcome(
            id=optimization, 
            name=optimization_objectives[optimization]["name"],
            original_value=original_value, 
            optimized_value=optimized_value,
            unit=optimization_objectives[optimization]["unit"]
            ))

    return res

def _create_optimization(optimization: OptimizationModel, claims) -> InsertOneResult:
    optimization.status = 'pending'
    document = jsons.dump(optimization, OptimizationModel, fork_inst = jsons_mongodb_fork, key_transformer = snakecase_to_camelcase, transform_dict_keys = False)
    user = update_user_table(claims)
    at = datetime.datetime.now(tz=datetime.timezone.utc)
    document = document | {
        'version': VERSION,
        'createdAt': at,
        'createdBy': user._id,
        'modifiedAt': at,
        'modifiedBy': user._id,
        'deletedAt': None,
        'deletedBy': None,
    }

    with client.start_session() as session:
        return database['optimizations'].insert_one(document, session = session)

def _get_history_datasets(model: OptimizationModelWithId, history: dict) -> list[ChartDatasetModel]:
    datasets: list[ChartDatasetModel] = []
    # TODO: better than O(history_length * variables_length)?
    for i, variable in enumerate(model.variables):
        # For each variable, collect the x, y pairs
        datasets.append(ChartDatasetModel(
            id=f'{variable.id}_{model.objective.id}',
            label=model.objective.name,
            description=variable.name,
            border_color='#0072BD',
            border_dash=[],
            hidden=False,
            data=sorted([{'x': val[i], 'y': y} for y, val in history], key=lambda h:h["x"])
        ))
    return datasets

def _chartify(model: OptimizationModelWithId, history: dict) -> OptimizationModelWithId:
    model.chart = OptimizationChart(
        datasets=_get_history_datasets(model, history)
    )
    return model

def _get_optimization_document(optimization_id: ObjectId) -> dict:
    with database['optimizations'].aggregate([{ '$match': { '_id': optimization_id } }] + _pipeline) as cursor:
        return next(cursor, None)

def _optimize_task(cell_design: CellDesignModel, optimization: OptimizationSettingsRequestModel, optimization_id: ObjectId, cell_design_id: ObjectId, claims):
    original_design = deepcopy(cell_design)
    optimization_result = None
    user = None
    at = datetime.datetime.now(tz=datetime.timezone.utc)
    try:
        optimization_result = optimize(cell_design, optimization, optimization_id)
        design_template = WeightDesignTemplate(cell_design)
        cell_design = design_template.apply_params([v.id for v in optimization.variables], optimization_result.optimized_values)

        cell_design_document = jsons.dump(design_template.cell_design, CellDesignModelWithMetadata, fork_inst = jsons_mongodb_fork, key_transformer = snakecase_to_camelcase, transform_dict_keys = False)

        user = update_user_table(claims)
        cell_design_document = cell_design_document | {
            'modifiedAt': at,
            'modifiedBy': user._id,
            'deletedAt': None,
            'deletedBy': None
        }

        database['cell-designs'].find_one_and_update({ '_id': cell_design_id }, { '$set': cell_design_document })

        optimization.status = 'complete'
    except Exception as e:
        traceback.print_exc()
        optimization.status = 'error'

    optimization_document = jsons.dump(optimization, OptimizationSettingsRequestModel, fork_inst = jsons_mongodb_fork, key_transformer = snakecase_to_camelcase, transform_dict_keys = False)
    user = user or update_user_table(claims)
    optimization_document = optimization_document | {
        'modifiedAt': at,
        'modifiedBy': user._id,
        'deletedAt': None,
        'deletedBy': None
    }

    if optimization_result and optimization_result.history:
        optimization_document['history'] = optimization_result.history
        optimization_document["variables"] = [optimization_document["variables"][i]|{"optimal":val} for i, val in enumerate(optimization_result.optimized_values)]
        
        optimization_document["optimization_outcome"] = []
        for optimization_outcome in _generate_outcome(original_design, cell_design):
            json_string = jsons.dump(optimization_outcome, OptimizationOutcome, fork_inst = jsons_mongodb_fork, key_transformer = snakecase_to_camelcase, transform_dict_keys = False)
            optimization_document["optimization_outcome"].append(json_string)

    database['optimizations'].find_one_and_update({'_id': optimization_id}, {'$set': optimization_document})

_pipeline = [
    {
        '$lookup': {
            'from': 'users',
            'localField': 'createdBy',
            'foreignField': '_id',
            'as': 'createdBy'
        }
    }, {
        '$unwind': {
            'path': '$createdBy',
            'preserveNullAndEmptyArrays': True
        }
    }, {
        '$lookup': {
            'from': 'users',
            'localField': 'modifiedBy',
            'foreignField': '_id',
            'as': 'modifiedBy'
        }
    }, {
        '$unwind': {
            'path': '$modifiedBy',
            'preserveNullAndEmptyArrays': True
        }
    }, {
        '$lookup': {
            'from': 'users',
            'localField': 'deletedBy',
            'foreignField': '_id',
            'as': 'deletedBy'
        }
    }, {
        '$unwind': {
            'path': '$deletedBy',
            'preserveNullAndEmptyArrays': True
        }
    }
]

@optimization_blueprint.post('/')
@bearer.secure('user')
def create_optimized_design():
    body = request.get_json()
    claims = bearer.claims

    content = jsons.load(body, OptimizationRequestModel, fork_inst = jsons_mongodb_fork, strip = True, key_transformer = camelcase_to_snakecase)
    cell_design = content.cell_design
    if cell_design.released and not bearer.check_roles('release'):
        return 'NOT ALLOWED', 405

    optimization = content.optimization
    optimization_result = _create_optimization(optimization, claims)
    optimization_id = optimization_result.inserted_id
    cell_design.optimization_id = optimization_result.inserted_id

    design_result = write_design(cell_design)

    thread = threading.Thread(target=_optimize_task, args=(cell_design, optimization, optimization_id, design_result.inserted_id, bearer.claims))
    thread.start()

    return get_design(design_result.inserted_id), 200

@optimization_blueprint.get('/<string:optimization_id>')
@bearer.secure('user')
def get_single_optimization(optimization_id: str):
    document = _get_optimization_document(ObjectId(optimization_id))
    if document is None:
        return 'NOT FOUND', 404

    history = document['history']
    model = jsons.load(_map_names_in_document(document), OptimizationModelWithId, fork_inst = jsons_flask_fork, key_transformer = camelcase_to_snakecase, transform_dict_keys = False)
    if history:
        model = _chartify(model, history)

    optimization_result = jsons.dump(model, OptimizationModelWithId, fork_inst = jsons_flask_fork, strict = True, key_transformer = snakecase_to_camelcase, transform_dict_keys = False)
    _calculate_optimization_percentage(optimization_result)
    if ObjectId(optimization_id) in Optimization._running_optimizations:
        optimization_result["progress"] = Optimization._running_optimizations[ObjectId(optimization_id)].progress()
    if optimization_result.get("status", "") == "complete":
        optimization_result["progress"] = 100.0
    return optimization_result

def _calculate_optimization_percentage(optimization_result):
    for optimization_outcome in optimization_result.get("optimizationOutcome", []) or []:
        optimized_value = optimization_outcome.get("optimizedValue", None)
        original_value = optimization_outcome.get("originalValue", None)
        optimization_id = optimization_outcome.get("id", None)

        if optimized_value is None or original_value is None or original_value == 0:
            optimization_outcome["percentageOptimization"] = None
            continue

        optimization_outcome["percentageOptimization"] = ((optimized_value - original_value) / original_value) * 100
        if optimization_objectives[optimization_id]['type'] == 'min':
            optimization_outcome["percentageOptimization"] *= -1
