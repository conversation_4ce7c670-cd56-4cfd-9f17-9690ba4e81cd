from dataclasses import dataclass, field
from typing import Literal, Optional, List

@dataclass
class ToleranceResultModel:
    id:str = None  
    name:str = None
    min_value:float = None
    standard_value:float = None
    max_value:float = None
    unit:str = None
    all_values:List[float] = None
    
@dataclass(unsafe_hash=True)
class ToleranceResponseModel:
    chained_result:Optional[ToleranceResultModel] = None
    variable_results: dict[str, List[ToleranceResultModel]] = field(default_factory=dict)