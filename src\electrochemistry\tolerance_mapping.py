from models.tolerance_settings_model import ToleranceSettingsModel, ToleranceSettingsVariable, ToleranceValueTypeOptions


settings:ToleranceSettingsModel = ToleranceSettingsModel(
    variables=[
        ToleranceSettingsVariable(id="anodeQAim", name="Anode Target Qrev C/10 [mAh/g]", unit="mAh/g"),
        ToleranceSettingsVariable(id="anodeQAimFirstCharge", name="Anode Target Q1st C/10 [mAh/g]", unit="mAh/g"),
        ToleranceSettingsVariable(id="cathodeQAim", name="Cathode Target Qrev C/10 [mAh/g]", unit="mAh/g"),
        ToleranceSettingsVariable(id="cathodeQAimFirstCharge", name="Cathode Target Q1st C/10 [mAh/g]", unit="mAh/g"),
        ToleranceSettingsVariable(id="anodeLoading", name="Anode Loading [mg/cm²]", unit="mg/cm²"),
        ToleranceSettingsVariable(id="cathodeLoading", name="Cathode Loading [mg/cm²]", unit="mg/cm²"),
        ToleranceSettingsVariable(id="npRatioRev", name="N/P Ratio Balanced", unit=None),
        ToleranceSettingsVariable(id="cathodeCoating", name="Cathode coathing thickness [µm]", unit="µm"),
        ToleranceSettingsVariable(id="anodeCoating", name="Anode coathing thickness [µm]", unit="µm"),
        ToleranceSettingsVariable(id="thicknessSeparator", name="Separator thickness [µm]", unit="µm"),
        ],
    value_type=[
        ToleranceValueTypeOptions(id="absolute", name="Absolute"),
        ToleranceValueTypeOptions(id="relative", name="Relative"),
        ToleranceValueTypeOptions(id="relative_percentage", name="Relative percentage"),
        
        ]

)
