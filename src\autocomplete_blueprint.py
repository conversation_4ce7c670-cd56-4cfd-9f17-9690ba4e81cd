import re
from typing import Optional
from auth import bearer
from flask import Blueprint, request
from database import database

autocomplete_blueprint = Blueprint('autocomplete_blueprint', __name__, url_prefix='/api/autocomplete')

def _get_autocomplete(field: str, search: Optional[str], limit: Optional[int]) -> list[str]:
    pipeline = []

    pipeline.append({ '$match': { field: { '$nin': [ '' ] } } })
    
    if search is not None and len(search) > 0:
        pattern = ''.join(f'(?=.*{re.escape(part)})' for part in search.split())
        pipeline.append({ '$match': { field: { '$regex': pattern, '$options': 'i' } } })

    pipeline.append({ '$group': { '_id': f'${field}' } })
    pipeline.append({ '$sort': { '_id': 1 } })

    if limit is not None and limit > 0:
        pipeline.append({ '$limit': limit })

    with database['cell-designs'].aggregate(pipeline) as cursor:
        return [item['_id'] for item in cursor]


@autocomplete_blueprint.get('/project')
@bearer.secure('user')
def get_project_autocomplete() -> list[str]:
    field = 'projectName'
    search = request.args.get('search', None, str)
    limit = request.args.get('limit', None, int)
    return _get_autocomplete(field, search, limit)

@autocomplete_blueprint.get('/project-state')
@bearer.secure('user')
def get_maturity_autocomplete() -> list[str]:
    field = 'projectState'
    search = request.args.get('search', None, str)
    limit = request.args.get('limit', None, int)
    return _get_autocomplete(field, search, limit)
