FROM amazonlinux:2023

# add yum repositories
RUN echo $'[mongodb-org-6.0] \n\
name     = MongoDB Repository \n\
baseurl  = https://repo.mongodb.org/yum/amazon/2023/mongodb-org/6.0/x86_64/ \n\
gpgcheck = 1 \n\
gpgkey   = https://www.mongodb.org/static/pgp/server-6.0.asc \n\
' > /etc/yum.repos.d/mongodb-org-6.0.repo

# install yum packages
RUN yum install -y python3.11
RUN yum install -y python3.11-pip
RUN yum install -y awscli-2
RUN yum install -y mongodb-mongosh-shared-openssl3
RUN yum install -y nano
RUN yum install -y less
RUN yum install -y jq

# install python requirements
COPY requirements.txt /
RUN python3.11 -m pip install --no-cache-dir -r requirements.txt

# copy application
WORKDIR /src
COPY src /src

# configure database connection
COPY global-bundle.pem /src
ENV MONGODB_TLS_CA_FILE="global-bundle.pem"
ENV MONGODB_DOCUMENTDB_MODE=1

# run application
EXPOSE 5000
CMD ["python3.11", "-m", "gunicorn" , "-b", "0.0.0.0:5000", "app:app"]
