from electrochemistry.active_material import ActiveMaterialWeight
from electrochemistry.active_material_blend import ActiveMaterialBlend
from electrochemistry.balancing import Balancing
from electrochemistry.cell_format import CellFormat, CellFormatCylinder, CellFormatPouch, CellFormatPrisma
from electrochemistry.cell_layer import CellLayer
from electrochemistry.component import Component
from electrochemistry.material_collection import material_collection
from electrochemistry.serialization import CellGeometry, default_format_definitions
from electrochemistry.electrode import Electrode
from electrochemistry.electrolyte import Electrolyte
from electrochemistry.full_cell import FullCell
from electrochemistry.material import MaterialWeight, leitruss_c65, cnt, binder_5130_solef, binder_paa_aquacharge
from electrochemistry.prelithiation import Prelithiation, PrelithiationProcess
from models.cell_design_model import CellDesignModel

class FullCellBuilder:
    def __init__(self, design):
        self._design = design
        self._cell_layer = None
        self._no_cell_layers = None
        self._balancing = None
        self._electrolyte = None
        self._cell_format = None

    def build(self) -> FullCell:
        self._create_cell_layer()
        self._create_electrolyte()
        self._create_cell_format()

        self._set_geometry()

        return FullCell(
            app = self._design,
            cell_layer = self._cell_layer,
            balancing = self._balancing,
            cell_format = self._cell_format,
            electrolyte = self._electrolyte
            )

    def set_no_cell_layers(self, number:int):
        self._no_cell_layers = number
        return self

    def calculate_tolerance_variables(self):
        full_cell = self.build()

        self._design.cathode_loading = full_cell.cell_layer.cathode.get_loading()
        self._design.anode_loading = full_cell.cell_layer.anode.get_loading()
        self._design.np_ratio_rev = full_cell.balancing.get_np_ratio_rev()
        self._design.anode_coating = full_cell.cell_layer.anode.coating_thickness
        self._design.cathode_coating = full_cell.cell_layer.cathode.coating_thickness

        return self

    def _create_cell_layer(self) -> CellLayer:
        al_cc = Component(material = material_collection.get_material(self._design.aluminium_current_collector_material_id))
        cu_cc = Component(material = material_collection.get_material(self._design.copper_current_collector_material_id))
        separator = Component(material = material_collection.get_material(self._design.separator_material_id))

        cell_layer = CellLayer(
            cathode = self._create_cathode(self._design),
            anode = self._create_anode(self._design),
            al_cc = al_cc,
            cu_cc = cu_cc,
            separator = separator
            )

        cell_layer.al_cc.set_thickness(thickness = self._design.thickness_aluminium_foil)
        cell_layer.cu_cc.set_thickness(thickness = self._design.thickness_copper_foil)
        cell_layer.separator.set_thickness(thickness = self._design.thickness_separator)
        self._cell_layer = cell_layer

        self._create_balancing()

        self._set_loading_areal_capacity()

        self._overwrite_coating_thickness_capacity()

        return cell_layer

    def _overwrite_coating_thickness_capacity(self):
        if hasattr(self._design, "anode_coating") and self._design.anode_coating is not None:
            self._cell_layer.anode.coating_thickness = self._design.anode_coating
        if hasattr(self._design, "cathode_coating") and self._design.cathode_coating is not None:
            self._cell_layer.cathode.coating_thickness = self._design.cathode_coating

    def _create_cathode(self, design: CellDesignModel) -> Electrode:
        if design.cathode_materials is not None and len(design.cathode_materials) > 0:
            active_material_blend = ActiveMaterialBlend(
                active_materials = [ActiveMaterialWeight(material_collection.get_active_material(m.material_id, m.material_date), m.weight_percent) for m in design.cathode_materials],
                formations = None,
                q_aim = design.cathode_q_aim,
                q_aim_first_charge = design.cathode_q_aim_first_charge,
                prelithiation = None
                )

            return Electrode(
                active_material = active_material_blend, active_material_weight = design.active_material_cathode_weight_percent,
                
                binder_materials = [MaterialWeight(material_collection.get_material(m.material_id), m.weight_percent) for m in design.cathode_binder_materials],
                conductive_additive_materials = [MaterialWeight(material_collection.get_material(m.material_id), m.weight_percent) for m in design.cathode_conductive_additive_materials],
                lithium_material = None, lithium_weight = 0,
                density = design.cathode_calander_density
                )
        else:
            return None

    def _create_anode(self, design: CellDesignModel) -> Electrode:
        if design.anode_materials is not None and len(design.anode_materials) > 0:
            process = PrelithiationProcess.NANOSCALE
            match design.prelithiation_process:
                case "nanoscale":
                    process = PrelithiationProcess.NANOSCALE
                case "group14":
                    process = PrelithiationProcess.GROUP_14

            prelithiation = Prelithiation(capacity = design.prelithiation_capacity if design.prelithiation_capacity is not None else 0, process = process)
            active_material_blend = ActiveMaterialBlend(
                active_materials = [ActiveMaterialWeight(material_collection.get_active_material(m.material_id, m.material_date), m.weight_percent) for m in design.anode_materials],
                formations = None,
                q_aim = design.anode_q_aim,
                q_aim_first_charge = design.anode_q_aim_first_charge,
                prelithiation = prelithiation
                )

            if prelithiation.process == PrelithiationProcess.NANOSCALE:
                lithium_weight_percent = 100 * prelithiation.get_lithium_weight()
                active_material_weight_percent = design.active_material_anode_weight_percent
            elif prelithiation.process == PrelithiationProcess.GROUP_14:
                lithium_weight_percent = 100 * (prelithiation.get_lithium_weight() / (1 + prelithiation.get_lithium_weight()))
                active_material_weight_percent = design.active_material_anode_weight_percent / (1 + prelithiation.get_lithium_weight())

            total = sum([active_material_weight_percent, sum([m.weight_percent for m in design.anode_binder_materials]), sum([m.weight_percent for m in design.anode_conductive_additive_materials]), lithium_weight_percent])

            return Electrode(
                active_material = active_material_blend, active_material_weight = active_material_weight_percent / total,
                binder_materials = [MaterialWeight(material_collection.get_material(m.material_id), m.weight_percent / total) for m in design.anode_binder_materials],
                conductive_additive_materials = [MaterialWeight(material_collection.get_material(m.material_id), m.weight_percent / total) for m in design.anode_conductive_additive_materials],
                lithium_material = prelithiation.lithium, lithium_weight = lithium_weight_percent / total,
                density = design.anode_calander_density
                )

        return None

    def _create_balancing(self) -> Balancing:
        balancing = Balancing(
            cathode = self._cell_layer.cathode.active_material,
            anode = self._cell_layer.anode.active_material,
            u_min = self._design.balancing_u_min,
            u_max = self._design.balancing_u_max,
            np_ratio_first = self._design.balancing_np_ratio_first
            )

        self._balancing = balancing
        if hasattr(self._design, "np_ratio_rev") and self._design.np_ratio_rev is not None:
            self._balancing.overwrite_np_ratio_rev(self._design.np_ratio_rev)
        return balancing

    def _set_loading_areal_capacity(self):
        if hasattr(self._design, "cathode_loading") and self._design.cathode_loading is not None:
            self._cell_layer.cathode.set_loading(self._design.cathode_loading)
        else:
            self._cell_layer.cathode.set_areal_capacity(self._design.cathode_areal_capacity)

        if hasattr(self._design, "anode_loading") and self._design.anode_loading is not None:
            self._cell_layer.anode.set_loading(self._design.anode_loading)
        else:
            self._cell_layer.anode.set_areal_capacity(self._design.cathode_areal_capacity * self._balancing.get_np_ratio_rev())

    def _create_electrolyte(self) -> Electrolyte:
        electrolyte_material = material_collection.get_material(self._design.electrolyte_material_id)
        electrolyte = Electrolyte(
            material = electrolyte_material,
            swelling = self._design.electrolyte_swelling / 100,
            amount = self._design.electrolyt_amount)
        self._electrolyte = electrolyte
        return electrolyte

    def _create_cell_format(self) -> CellFormat:
        definition = default_format_definitions[self._design.cell_format_id]
        match definition.geometry:
            case CellGeometry.POUCH:
                cls = CellFormatPouch
            case CellGeometry.PRISMA:
                cls = CellFormatPrisma
            case CellGeometry.CYLINDER:
                cls = CellFormatCylinder

        cell_format = cls(
            id = definition.id,
            properties = definition.default_properties if self._design.cell_format_is_default else self._design.cell_format_properties,
            cathode_tab = definition.cathode_tab,
            anode_tab = definition.anode_tab,
            housing = definition.housing,
            cell_layer = self._cell_layer,
            electrolyte = self._electrolyte
        )

        self._cell_format = cell_format

        self._cell_format.calculate_cell_layers(self._no_cell_layers)
        return cell_format

    def _set_geometry(self):
        self._cell_layer.al_cc.set_geometry(area = self._cell_format.cathode_coating_area, thickness = self._design.thickness_aluminium_foil)
        self._cell_layer.cu_cc.set_geometry(area = self._cell_format.anode_coating_area, thickness = self._design.thickness_copper_foil)
        self._cell_layer.separator.set_geometry(area = self._cell_format.separator_area, thickness = self._design.thickness_separator)

        self._cell_layer.cathode.substrate = self._cell_layer.al_cc
        self._cell_layer.anode.substrate = self._cell_layer.cu_cc
        self._cell_layer.cathode.set_geometry(area = self._cell_format.cathode_coating_area, thickness = self._cell_layer.cathode.get_thickness())
        self._cell_layer.anode.set_geometry(area = self._cell_format.anode_coating_area, thickness = self._cell_layer.anode.get_thickness())

def create_full_cell(design: CellDesignModel) -> FullCell:
    return FullCellBuilder(design).build()
