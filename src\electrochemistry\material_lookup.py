import pandas as pd


def _load_csv_file(file: str) -> pd.DataFrame:
    return pd.read_csv(file, index_col=0)


class MaterialLookup:

    data: pd.DataFrame
    material_id: str

    def __init__(self, material_id, path: str):
        data = _load_csv_file(path)
        data = data.astype(float)

        self.material_id = material_id
        self.data = data

    def get_value(self, lookup_value) -> float:
        return self.data.loc[lookup_value, self.data.columns[0]]
