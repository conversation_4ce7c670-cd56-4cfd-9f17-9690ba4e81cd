from auth import bearer
from flask import Blueprint
import jsons

from jsons_config import jsons_flask_fork
from models.optimization_settings_model import OptimizationSettingsModel
from electrochemistry.optimization_mapping import settings
from utils.camelcase_to_snakecase import snakecase_to_camelcase

optimization_settings_blueprint = Blueprint('optimization_settings_api', __name__, url_prefix='/api/optimization-settings')

@optimization_settings_blueprint.get('/')
@bearer.secure('user')
def get_settings():
    return jsons.dump(settings, OptimizationSettingsModel, fork_inst = jsons_flask_fork, key_transformer = snakecase_to_camelcase)
