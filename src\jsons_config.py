import datetime
import enum
import math
from typing import Any, Optional
import jsons
from jsons.deserializers.default_object import default_object_deserializer
from jsons.deserializers.default_enum import default_enum_deserializer
from jsons.serializers.default_dict import default_dict_serializer
from bson import ObjectId

jsons_flask_fork = jsons.fork()

def _get_keys(cls: type) -> set[str]:
    keys = set()
    while cls is not None:
        if hasattr(cls, '__annotations__'):
            keys.update(cls.__annotations__.keys())
        cls = cls.__base__ if hasattr(cls, '__base__') else None

    return keys

def object_id_serializer(obj: ObjectId, **kwargs) -> str:
    return str(obj)

def object_id_deserializer(obj: str, cls: type = ObjectId, **kwargs) -> ObjectId:
    return ObjectId(obj)

def float_serializer(obj: float, **kwargs) -> Optional[float]:
    return None if math.isnan(obj) else obj

def object_deserializer(obj: dict[str, Any], cls: type, fork_inst, key_transformer, strip: bool = False, **kwargs):
    if strip:
        keys = _get_keys(cls)
        obj = { key: value for key, value in obj.items() if key_transformer(key) in keys }

    if issubclass(cls, enum.Enum):
        return default_enum_deserializer(obj, cls, fork_inst = fork_inst, key_transformer = key_transformer, strip = strip, **kwargs)
    else:
        return default_object_deserializer(obj, cls, fork_inst = fork_inst, key_transformer = key_transformer, strip = strip, **kwargs)

def dict_serializer(obj: dict, fork_inst, key_transformer = None, transform_dict_keys: bool = True, **kwargs) -> dict:
    return default_dict_serializer(obj, fork_inst = fork_inst, key_transformer = key_transformer if transform_dict_keys else None, **kwargs)

jsons.set_serializer(object_id_serializer, ObjectId, fork_inst = jsons_flask_fork)
jsons.set_deserializer(object_id_deserializer, ObjectId, fork_inst = jsons_flask_fork)
jsons.set_serializer(float_serializer, float, fork_inst = jsons_flask_fork)
jsons.set_deserializer(object_deserializer, object, fork_inst = jsons_flask_fork)
jsons.set_serializer(dict_serializer, dict, fork_inst = jsons_flask_fork)

#
# Mongo DB specific serializer
#

jsons_mongodb_fork = jsons.fork(jsons_flask_fork)

def mongodb_object_id_serializer(obj: ObjectId, **kwargs) -> ObjectId:
    return obj

def mongodb_object_id_deserializer(obj: str, cls: type = ObjectId, **kwargs) -> ObjectId:
    return ObjectId(obj)

def mongodb_datetime_serializer(obj: datetime.datetime, **kwargs) -> datetime.datetime:
    return obj

def mongodb_datetime_deserializer(obj: Optional[str], cls: type = ObjectId, **kwargs) -> Optional[datetime.datetime]:
    return datetime.datetime.fromisoformat(obj) if obj is not None else None

jsons.set_serializer(mongodb_object_id_serializer, ObjectId, fork_inst = jsons_mongodb_fork)
jsons.set_deserializer(mongodb_object_id_deserializer, ObjectId, fork_inst = jsons_mongodb_fork)
jsons.set_serializer(mongodb_datetime_serializer, datetime.datetime, fork_inst = jsons_mongodb_fork)
jsons.set_deserializer(mongodb_datetime_deserializer, datetime.datetime, fork_inst = jsons_mongodb_fork)
