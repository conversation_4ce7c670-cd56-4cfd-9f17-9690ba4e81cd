from auth import bearer
from flask import Blueprint
import jsons

from jsons_config import jsons_flask_fork
from electrochemistry.tolerance_mapping import settings
from models.tolerance_settings_model import ToleranceSettingsModel
from utils.camelcase_to_snakecase import snakecase_to_camelcase

tolerance_settings_blueprint = Blueprint('tolerance_settings_api', __name__, url_prefix='/api/tolerance-settings')

@tolerance_settings_blueprint.get('/')
@bearer.secure('user')
def get_settings():
    return jsons.dump(settings, ToleranceSettingsModel, fork_inst = jsons_flask_fork, key_transformer = snakecase_to_camelcase)
