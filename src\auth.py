from oidc_bearer import OpenIdConnectBearer
import os

mock_claims = {
    'oid': 'd2e9e783-4581-478e-a24c-ed51e47c5e94',
    'name': 'Test User',
    'scp': 'openid profile',
    'sub': '<EMAIL>',
    'tid': '9188040d-6c67-4c5b-b112-36a304b66dad',
    'upn': '<EMAIL>',
    'roles': [
        'user',
        'release',
        'admin'
    ],
    'ver': '2.0'
}

use_mock_bearer = os.getenv('USE_MOCK_BEARER') is not None
oidc_endpoint = os.getenv('OIDC_ENDPOINT', 'https://login.microsoftonline.com/0f4b4464-5743-4f40-b3a6-fa3ca6b04f30/v2.0')
oidc_audience = os.getenv('OIDC_AUDIENCE', 'f0edd3d0-1258-4764-bfa1-3a2a151abf9e')

bearer = OpenIdConnectBearer(
    endpoint = oidc_endpoint,
    audience = oidc_audience,
    mock_claims = mock_claims if use_mock_bearer else None
    )
