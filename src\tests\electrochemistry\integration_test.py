import unittest
from electrochemistry.serialization import CellFormatPouchProperties
from electrochemistry.factory import create_full_cell

from models.cell_design_model import CellDesignModel, InactiveMaterialWeightModel, MaterialWeightModel
from cell_design_metrics_blueprint import calculate

class TestCreatingElectrochemistryModel(unittest.TestCase):

    default_cell_design = CellDesignModel(
        cathode_materials=[MaterialWeightModel(material_id="basf_ncm83_pc", weight_percent=100)],
        cathode_q_aim=None,
        anode_materials=[MaterialWeightModel(material_id="g14_btr_blend_70_247", weight_percent=100)],
        anode_q_aim=None,
        prelithiation_capacity=0.0,
        balancing_np_ratio_first=1.1,
        balancing_u_max=4.24,
        balancing_u_min=2.5,
        active_material_anode_weight_percent = 94.7,
        active_material_cathode_weight_percent = 96.0,

        #binder_material_anode_weight_percent = 4.0,
        anode_binder_materials = [InactiveMaterialWeightModel(material_id = 'binder_paa_aquacharge', weight_percent = 4.0)],

        # leitruss_anode_weight_percent = 1.25,
        # cnt_anode_weight_percent = 0.05,
        anode_conductive_additive_materials = [
            InactiveMaterialWeightModel(material_id = 'leitruss_c65', weight_percent = 1.25),
            InactiveMaterialWeightModel(material_id = 'cnt', weight_percent = 0.05)
        ],

        # binder_material_cathode_weight_percent = 2.0,
        cathode_binder_materials = [InactiveMaterialWeightModel(material_id = 'binder_5130_solef', weight_percent = 2.0)],

        # leitruss_cathode_weight_percent = 2.0,
        # cnt_cathode_weight_percent = 0.0,
        cathode_conductive_additive_materials = [
            InactiveMaterialWeightModel(material_id = 'leitruss_c65', weight_percent = 2.0),
            InactiveMaterialWeightModel(material_id = 'cnt', weight_percent = 0.0)
        ],

        prelithiation_process='nanoscale',
        cathode_calander_density = 3.5,
        anode_calander_density = 1.0,
        cathode_areal_capacity = 4.0,
        thickness_aluminium_foil = 12.0,
        thickness_copper_foil = 8.0,
        thickness_separator = 18.0,
        cell_format_id = 'cf1_thin',
        cell_format_is_default = False,
        cell_format_properties = CellFormatPouchProperties(
            coating_length_cathode = 316,
            coating_width_cathode = 94,
            coating_length_anode = 318,
            coating_width_anode = 95,
            coating_length_separator = 322,
            coating_width_separator = 97,
            housing_thickness = 0.183,
            swelling_buffer = 0,
            housing_weight = 17.0,
            cell_length = 354,
            cell_width = 102,
            cell_thickness = 10.72
        ),
        electrolyte_swelling = 3,
        electrolyt_amount = 1.4,
        aging_table_edit_values = {},
        safety = 0.95,
        parallel_cells_count = 2,
        serial_cells_count = 198,
        module_count = 1,
        business_case_id = '100 MWh',
        scrap = 1,
        bom_prices = {}
    )

    cell_design_with_prelithiation_nanoscale = CellDesignModel(
        cathode_materials=[MaterialWeightModel(material_id="basf_ncm83_pc", weight_percent=100)],
        cathode_q_aim=None,
        anode_materials=[MaterialWeightModel(material_id="g14_btr_blend_70_247", weight_percent=100)],
        anode_q_aim=None,
        prelithiation_capacity=50.0,
        balancing_np_ratio_first=1.1,
        balancing_u_max=4.24,
        balancing_u_min=2.5,
        active_material_anode_weight_percent = 94.7,
        active_material_cathode_weight_percent = 96.0,

        #binder_material_anode_weight_percent = 4.0,
        anode_binder_materials = [InactiveMaterialWeightModel(material_id = 'binder_paa_aquacharge', weight_percent = 4.0)],

        # leitruss_anode_weight_percent = 1.25,
        # cnt_anode_weight_percent = 0.05,
        anode_conductive_additive_materials = [
            InactiveMaterialWeightModel(material_id = 'leitruss_c65', weight_percent = 1.25),
            InactiveMaterialWeightModel(material_id = 'cnt', weight_percent = 0.05)
        ],

        # binder_material_cathode_weight_percent = 2.0,
        cathode_binder_materials = [InactiveMaterialWeightModel(material_id = 'binder_5130_solef', weight_percent = 2.0)],

        # leitruss_cathode_weight_percent = 2.0,
        # cnt_cathode_weight_percent = 0.0,
        cathode_conductive_additive_materials = [
            InactiveMaterialWeightModel(material_id = 'leitruss_c65', weight_percent = 2.0),
            InactiveMaterialWeightModel(material_id = 'cnt', weight_percent = 0.0)
        ],

        prelithiation_process='nanoscale',
        cathode_calander_density = 3.5,
        anode_calander_density = 1.0,
        cathode_areal_capacity = 4.0,
        thickness_aluminium_foil = 12.0,
        thickness_copper_foil = 8.0,
        thickness_separator = 18.0,
        cell_format_id = 'cf1_thin',
        cell_format_is_default = False,
        cell_format_properties = CellFormatPouchProperties(
            coating_length_cathode = 316,
            coating_width_cathode = 94,
            coating_length_anode = 318,
            coating_width_anode = 95,
            coating_length_separator = 322,
            coating_width_separator = 97,
            housing_thickness = 0.183,
            swelling_buffer = 0,
            housing_weight = 17.0,
            cell_length = 354,
            cell_width = 102,
            cell_thickness = 10.72
        ),
        electrolyte_swelling = 3,
        electrolyt_amount = 1.4,
        aging_table_edit_values = {},
        safety = 0.95,
        parallel_cells_count = 2,
        serial_cells_count = 198,
        module_count = 1,
        business_case_id = '100 MWh',
        scrap = 1,
        bom_prices = {}
    )

    def test_create_full_cell_default_values(self):
        full_cell = create_full_cell(self.default_cell_design)
        self.assertEqual(full_cell.cell_format.volume / 1000, 387.07776)
        self.assertEqual(full_cell.cell_format.cathode_coating_area, 29704)
        self.assertEqual(full_cell.cell_format.anode_coating_area, 30210)
        self.assertEqual(full_cell.cell_format.separator_area, 31234)

    def test_calculate_default_cell_design(self):
        metrics = calculate(self.default_cell_design)
        self.assertAlmostEqual(metrics.materials.anode.active_material_density, 2.1, 1)
        self.assertAlmostEqual(metrics.materials.anode.material1_weight_percent, 70.00, 2)
        self.assertAlmostEqual(metrics.materials.anode.material1_density, 2.00, 2)
        self.assertAlmostEqual(metrics.materials.anode.material2_weight_percent, 24.70, 2)
        self.assertAlmostEqual(metrics.materials.anode.material2_density, 2.26, 2)
        self.assertAlmostEqual(metrics.materials.anode.binder_densities[0], 1.10, 2)
        self.assertAlmostEqual(metrics.materials.anode.conductive_additive_densities[0], 1.60, 2)
        self.assertAlmostEqual(metrics.materials.anode.conductive_additive_densities[1], 1.60, 2)
        self.assertAlmostEqual(metrics.materials.anode.total_density, 1.99, 2)
        self.assertAlmostEqual(metrics.materials.anode.full_cell_qrev, 1241, 0)
        self.assertAlmostEqual(metrics.materials.anode.half_cell_qrev, 1449, 0)
        self.assertAlmostEqual(metrics.materials.cathode.active_material_density, 4.78, 2)
        self.assertAlmostEqual(metrics.materials.cathode.material1_weight_percent, 96.00, 2)
        self.assertAlmostEqual(metrics.materials.cathode.material1_density, 4.78, 2)
        self.assertAlmostEqual(metrics.materials.cathode.material2_weight_percent, None, 2)
        self.assertAlmostEqual(metrics.materials.cathode.material2_density, None, 2)
        self.assertAlmostEqual(metrics.materials.cathode.binder_densities[0], 1.77, 2)
        self.assertAlmostEqual(metrics.materials.cathode.conductive_additive_densities[0], 1.60, 2)
        self.assertAlmostEqual(metrics.materials.cathode.conductive_additive_densities[1], 1.60, 2)
        self.assertAlmostEqual(metrics.materials.cathode.total_density, 4.45, 2)
        self.assertAlmostEqual(metrics.materials.cathode.full_cell_qrev, 204, 0)
        self.assertAlmostEqual(metrics.materials.cathode.half_cell_qrev, 217, 0)
        self.assertAlmostEqual(metrics.materials.prelithiation.lithium_weight_percent, 0.00, 2)
        self.assertAlmostEqual(metrics.materials.prelithiation.lithium_density, 0.53, 2)
        self.assertAlmostEqual(metrics.materials.prelithiation.active_material_weight_percent, 94.70, 2)
        self.assertAlmostEqual(metrics.materials.prelithiation.conductive_additive_weight_percents[0], 1.25, 2)
        self.assertAlmostEqual(metrics.materials.prelithiation.material1_weight_percent, 70.00, 2)
        self.assertAlmostEqual(metrics.materials.prelithiation.material2_weight_percent, 24.70, 2)
        self.assertAlmostEqual(metrics.materials.prelithiation.binder_weight_percents[0], 4.00, 2)
        self.assertAlmostEqual(metrics.materials.prelithiation.conductive_additive_weight_percents[1], 0.050, 3)
        self.assertAlmostEqual(metrics.materials.electrolyte_density, 1.21, 2)
        self.assertAlmostEqual(metrics.materials.separator_density, 1.10, 2)
        self.assertAlmostEqual(metrics.materials.separator_porousness, 45, 0)
        self.assertAlmostEqual(metrics.materials.aluminium_density, 2.70, 2)
        self.assertAlmostEqual(metrics.materials.copper_density, 8.96, 2)

        # electrode pair
        self.assertAlmostEqual(metrics.electrode_pair.cathode_porosity, 21, 0)
        self.assertAlmostEqual(metrics.electrode_pair.anode_porosity, 50, 0)
        self.assertAlmostEqual(metrics.electrode_pair.balancing, 1.08, 2)
        self.assertAlmostEqual(metrics.electrode_pair.anode_area_capacity, 4.33, 2)
        self.assertAlmostEqual(metrics.electrode_pair.cathode_loading, 19.28, 2)
        self.assertAlmostEqual(metrics.electrode_pair.anode_loading, 3.21, 2)
        self.assertAlmostEqual(metrics.electrode_pair.cathode_coating_thickness, 55, 0)
        self.assertAlmostEqual(metrics.electrode_pair.anode_coating_thickness, 32, 0)
        self.assertAlmostEqual(metrics.electrode_pair.cell_layer_thickness, 115, 0)
        self.assertAlmostEqual(metrics.electrode_pair.cathode_thickness, 122, 0)
        self.assertAlmostEqual(metrics.electrode_pair.anode_thickness, 72, 0)

    def test_calculate_cell_design_with_prelithiation_nanoscale(self):
        metrics = calculate(self.cell_design_with_prelithiation_nanoscale)
        self.assertAlmostEqual(metrics.materials.anode.active_material_density, 2.1, 1)
        self.assertAlmostEqual(metrics.materials.anode.material1_weight_percent, 70.00, 2)
        self.assertAlmostEqual(metrics.materials.anode.material1_density, 2.00, 2)
        self.assertAlmostEqual(metrics.materials.anode.material2_weight_percent, 24.70, 2)
        self.assertAlmostEqual(metrics.materials.anode.material2_density, 2.26, 2)
        self.assertAlmostEqual(metrics.materials.anode.binder_densities[0], 1.10, 2)
        self.assertAlmostEqual(metrics.materials.anode.conductive_additive_densities[0], 1.60, 2)
        self.assertAlmostEqual(metrics.materials.anode.conductive_additive_densities[1], 1.60, 2)
        self.assertAlmostEqual(metrics.materials.anode.total_density, 1.92, 2)
        self.assertAlmostEqual(metrics.materials.anode.full_cell_qrev, 1225, 0)
        self.assertAlmostEqual(metrics.materials.anode.half_cell_qrev, 1449, 0)
        self.assertAlmostEqual(metrics.materials.cathode.active_material_density, 4.78, 2)
        self.assertAlmostEqual(metrics.materials.cathode.material1_weight_percent, 96.00, 2)
        self.assertAlmostEqual(metrics.materials.cathode.material1_density, 4.78, 2)
        self.assertAlmostEqual(metrics.materials.cathode.material2_weight_percent, None, 2)
        self.assertAlmostEqual(metrics.materials.cathode.material2_density, None, 2)
        self.assertAlmostEqual(metrics.materials.cathode.binder_densities[0], 1.77, 2)
        self.assertAlmostEqual(metrics.materials.cathode.conductive_additive_densities[0], 1.60, 2)
        self.assertAlmostEqual(metrics.materials.cathode.conductive_additive_densities[1], 1.60, 2)
        self.assertAlmostEqual(metrics.materials.cathode.total_density, 4.45, 2)
        self.assertAlmostEqual(metrics.materials.cathode.full_cell_qrev, 208, 0)
        self.assertAlmostEqual(metrics.materials.cathode.half_cell_qrev, 217, 0)
        self.assertAlmostEqual(metrics.materials.prelithiation.lithium_weight_percent, 1.27, 2)
        self.assertAlmostEqual(metrics.materials.prelithiation.lithium_density, 0.53, 2)
        self.assertAlmostEqual(metrics.materials.prelithiation.active_material_weight_percent, 93.49, 2)
        self.assertAlmostEqual(metrics.materials.prelithiation.conductive_additive_weight_percents[0], 1.23, 2)
        self.assertAlmostEqual(metrics.materials.prelithiation.material1_weight_percent, 69.11, 2)
        self.assertAlmostEqual(metrics.materials.prelithiation.material2_weight_percent, 24.39, 2)
        self.assertAlmostEqual(metrics.materials.prelithiation.binder_weight_percents[0], 3.95, 2)
        self.assertAlmostEqual(metrics.materials.prelithiation.conductive_additive_weight_percents[1], 0.049, 3)
        self.assertAlmostEqual(metrics.materials.electrolyte_density, 1.21, 2)
        self.assertAlmostEqual(metrics.materials.separator_density, 1.10, 2)
        self.assertAlmostEqual(metrics.materials.separator_porousness, 45, 0)
        self.assertAlmostEqual(metrics.materials.aluminium_density, 2.70, 2)
        self.assertAlmostEqual(metrics.materials.copper_density, 8.96, 2)

    def test_calculate_cell_design_with_cell_format_cf3(self):
        design = self.default_cell_design
        design.cell_format_id = 'cf3'
        design.cell_format_is_default = True
        design.cell_format_properties = None

        metrics = calculate(design)

        self.assertAlmostEqual(metrics.cell.cathode_coating_area, 24750, 0)

    def test_calculate_cell_design_with_cell_format_eas_4690(self):
        design = self.default_cell_design
        design.cell_format_id = 'eas_4690'
        design.cell_format_is_default = True
        design.cell_format_properties = None

        metrics = calculate(design)

        self.assertAlmostEqual(metrics.cell.cathode_coating_area, 452016, 0)
