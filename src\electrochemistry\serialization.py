from dataclasses import dataclass
from enum import Enum
import json
import os
from typing import Any, Generic, Optional, TypeVar
from electrochemistry.component import Component
from electrochemistry.material import Material
from electrochemistry.material_collection import material_collection

from utils.camelcase_to_snakecase import camelcase_to_snakecase

@dataclass()
class CellFormatProperties:
    coating_width_cathode: float
    coating_width_anode: float
    coating_width_separator: float
    housing_thickness: float
    swelling_buffer: float
    housing_weight: Optional[float]

@dataclass()
class CellFormatPouchProperties(CellFormatProperties):
    coating_length_cathode: float
    coating_length_anode: float
    coating_length_separator: float
    cell_length: float
    cell_width: float
    cell_thickness: float

@dataclass()
class CellFormatPrismaProperties(CellFormatPouchProperties):
    has_two_substacks: bool

@dataclass()
class CellFormatCylinderProperties(CellFormatProperties):
    coating_length_cathode: Optional[float]
    coating_length_anode: Optional[float]
    coating_length_separator: Optional[float]
    cell_diameter: float
    cell_height: float
    cell_core_diameter: float
    anode_overhang: float

class CellGeometry(Enum):
    POUCH = 1
    PRISMA = 2
    CYLINDER = 3

TProperties = TypeVar('TProperties', CellFormatPouchProperties, CellFormatPrismaProperties, CellFormatCylinderProperties)
@dataclass
class CellFormatDefinition(Generic[TProperties]):
    id: str
    name: str
    geometry: CellGeometry
    anode_tab: Component
    cathode_tab: Component
    housing: Material
    default_properties: TProperties

    @staticmethod
    def from_dict(dict: dict) -> 'CellFormatDefinition':
        match dict['geometry']:
            case 'POUCH':
                cls = CellFormatPouchProperties
            case 'PRISMA':
                cls = CellFormatPrismaProperties
            case 'CYLINDER':
                cls = CellFormatCylinderProperties
        
        return CellFormatDefinition[cls](
            id = dict['id'],
            name = dict['name'],
            geometry = CellGeometry[dict['geometry']],
            anode_tab = Component.from_dict(dict['anode_tab']),
            cathode_tab = Component.from_dict(dict['cathode_tab']),
            housing = material_collection.get_material(dict['housing_material_id']),
            default_properties = cls(**dict['defaults'])
        )

class SnakeCaseDecoder(json.JSONDecoder):

    def __init__(self):
        json.JSONDecoder.__init__(self, object_pairs_hook = lambda pairs: SnakeCaseDecoder.object_pairs_hook(pairs))

    def object_pairs_hook(pairs: list[tuple]) -> list[tuple]:
        return { camelcase_to_snakecase(key): value for (key, value) in pairs }

def _load_json_file(file: str) -> Any:
    with open(file, "rb") as f:
        return json.load(f, cls = SnakeCaseDecoder)

def _load_format_definitions(file: str) -> dict[str, CellFormatDefinition]:
    formats = _load_json_file(file)
    return { format['id']: CellFormatDefinition.from_dict(format) for format in formats }

default_format_definitions = _load_format_definitions(os.path.join(os.getcwd(), 'data', 'formats.json'))
