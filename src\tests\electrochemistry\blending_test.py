import unittest
import pandas as pd

from electrochemistry.blending import blend_single_formation, get_u_range
from electrochemistry.formation_table import Formation

# u slope: -0,5
test_cycle_q_increasing_u_decreasing_1 = pd.DataFrame({
    'LadungMAhg':   [ 0.00, 1.00, 2.00, 3.00, 4.00, 5.00, 6.00, 7.00, 8.00, 9.00 ],
    'SpannungV':    [ 4.50, 4.00, 3.50, 3.00, 2.50, 2.00, 1.50, 1.00, 0.50, 0.00 ]
})

# u slope: -0,3
test_cycle_q_increasing_u_decreasing_2 = pd.DataFrame({
    'LadungMAhg':   [ 1.00, 2.00, 4.00, 6.00, 8.00, 10.00 ],
    'SpannungV':    [ 4.00, 3.00, 2.50, 2.00, 1.50,  1.00 ]
})

# u slope: 0,5
test_cycle_q_increasing_u_increasing_1 = pd.DataFrame({
    'LadungMAhg':   [ 0.00, 1.00, 2.00, 3.00, 4.00, 5.00, 6.00, 7.00, 8.00, 9.00 ],
    'SpannungV':    [ 0.00, 0.50, 1.00, 1.50, 2.00, 2.50, 3.00, 3.50, 4.00, 4.50 ]
})

# u slope: 0,3
test_cycle_q_increasing_u_increasing_2 = pd.DataFrame({
    'LadungMAhg':   [ 1.00, 2.00, 4.00, 6.00, 8.00, 10.00 ],
    'SpannungV':    [ 1.00, 1.50, 2.00, 2.50, 3.00,  4.00 ]
})

class TestGetURange(unittest.TestCase):

    def test_get_u_range_single_decreasing_exact(self):
        (u_start, u_end) = get_u_range(q_ends=[0.00], formations=[Formation('', test_cycle_q_increasing_u_decreasing_1)], weights=[1])
        self.assertEqual(u_start, 4.50)
        self.assertEqual(u_end, 0.00)

        (u_start, u_end) = get_u_range(q_ends=[2.00], formations=[Formation('', test_cycle_q_increasing_u_decreasing_1)], weights=[1])
        self.assertEqual(u_start, 3.50)
        self.assertEqual(u_end, 0.00)

        (u_start, u_end) = get_u_range(q_ends=[4.00], formations=[Formation('', test_cycle_q_increasing_u_decreasing_1)], weights=[1])
        self.assertEqual(u_start, 2.50)
        self.assertEqual(u_end, 0.00)

        (u_start, u_end) = get_u_range(q_ends=[6.00], formations=[Formation('', test_cycle_q_increasing_u_decreasing_1)], weights=[1])
        self.assertEqual(u_start, 1.50)
        self.assertEqual(u_end, 0.00)

        (u_start, u_end) = get_u_range(q_ends=[8.00], formations=[Formation('', test_cycle_q_increasing_u_decreasing_1)], weights=[1])
        self.assertEqual(u_start, 0.50)
        self.assertEqual(u_end, 0.00)

    def test_get_u_range_single_decreasing_interpolate(self):
        (u_start, u_end) = get_u_range(q_ends=[5.50], formations=[Formation('', test_cycle_q_increasing_u_decreasing_1)], weights=[1])
        self.assertEqual(u_start, 1.75)
        self.assertEqual(u_end, 0.00)

        (u_start, u_end) = get_u_range(q_ends=[2.10], formations=[Formation('', test_cycle_q_increasing_u_decreasing_1)], weights=[1])
        self.assertEqual(u_start, 3.45)
        self.assertEqual(u_end, 0.00)

    def test_get_u_range_multiple_decreasing_interpolate(self):
        (u_start, u_end) = get_u_range(q_ends=[2.50, 7.00], formations=[Formation('', test_cycle_q_increasing_u_decreasing_1), Formation('', test_cycle_q_increasing_u_decreasing_2)], weights=[0.5, 0.5])
        self.assertEqual(u_start, 3.25)
        self.assertEqual(u_end, 1.00)

        (u_start, u_end) = get_u_range(q_ends=[7.00, 7.00], formations=[Formation('', test_cycle_q_increasing_u_decreasing_1), Formation('', test_cycle_q_increasing_u_decreasing_2)], weights=[0.5, 0.5])
        self.assertEqual(u_start, 1.75)
        self.assertEqual(u_end, 1.00)

    def test_get_u_range_single_increasing_exact(self):
        (u_start, u_end) = get_u_range(q_ends=[0.00], formations=[Formation('', test_cycle_q_increasing_u_increasing_1)], weights=[1.0])
        self.assertEqual(u_start, 0.00)
        self.assertEqual(u_end, 4.50)

        (u_start, u_end) = get_u_range(q_ends=[2.00], formations=[Formation('', test_cycle_q_increasing_u_increasing_1)], weights=[1.0])
        self.assertEqual(u_start, 1.00)
        self.assertEqual(u_end, 4.50)

        (u_start, u_end) = get_u_range(q_ends=[4.00], formations=[Formation('', test_cycle_q_increasing_u_increasing_1)], weights=[1.0])
        self.assertEqual(u_start, 2.00)
        self.assertEqual(u_end, 4.50)

        (u_start, u_end) = get_u_range(q_ends=[6.00], formations=[Formation('', test_cycle_q_increasing_u_increasing_1)], weights=[1.0])
        self.assertEqual(u_start, 3.00)
        self.assertEqual(u_end, 4.50)

        (u_start, u_end) = get_u_range(q_ends=[8.00], formations=[Formation('', test_cycle_q_increasing_u_increasing_1)], weights=[1.0])
        self.assertEqual(u_start, 4.00)
        self.assertEqual(u_end, 4.50)

    def test_get_u_range_single_increasing_interpolate(self):
        (u_start, u_end) = get_u_range(q_ends=[5.50], formations=[Formation('', test_cycle_q_increasing_u_increasing_1)], weights=[1.0])
        self.assertEqual(u_start, 2.75)
        self.assertEqual(u_end, 4.50)

        (u_start, u_end) = get_u_range(q_ends=[2.10], formations=[Formation('', test_cycle_q_increasing_u_increasing_1)], weights=[1.0])
        self.assertEqual(u_start, 1.05)
        self.assertEqual(u_end, 4.50)

    def test_get_u_range_multiple_increasing_interpolate(self):
        (u_start, u_end) = get_u_range(q_ends=[2.50, 7.00], formations=[Formation('', test_cycle_q_increasing_u_increasing_1), Formation('', test_cycle_q_increasing_u_increasing_2)], weights=[0.5, 0.5])
        self.assertEqual(u_start, 1.25)
        self.assertEqual(u_end, 4.00)

        (u_start, u_end) = get_u_range(q_ends=[7.00, 7.00], formations=[Formation('', test_cycle_q_increasing_u_increasing_1), Formation('', test_cycle_q_increasing_u_increasing_2)], weights=[0.5, 0.5])
        self.assertEqual(u_start, 2.75)
        self.assertEqual(u_end, 4.00)

class TestBlendSingleFormation(unittest.TestCase):

    def test_blend_single_formation_decreasing_single_sample(self):
        q_ends = [ 0.00, 0.00 ]
        u_samples = pd.Series([ 2.00 ])
        formations = [ Formation('', test_cycle_q_increasing_u_decreasing_1), Formation('', test_cycle_q_increasing_u_decreasing_2) ]
        weights = [ 0.20, 0.80 ]
        df = blend_single_formation(q_ends, u_samples, formations, weights)
        self.assertEqual(len(df.index), 1)
        self.assertAlmostEqual(df['SpannungV'].iloc[0], 2.00)
        self.assertAlmostEqual(df['LadungMAhg'].iloc[0], 5.80) # = 5.00 * 0.20 + 6.00 * 0.80

    def test_blend_single_formation_decreasing_multiple_samples_interpolated(self):
        q_ends = [ 0.00, 0.00 ]
        u_samples = pd.Series([ 4.00, 3.75, 3.50, 3.25, 3.00, 2.75, 2.50, 2.25, 2.00, 1.75, 1.50, 1.25, 1.00 ])
        formations = [ Formation('', test_cycle_q_increasing_u_decreasing_1), Formation('', test_cycle_q_increasing_u_decreasing_2) ]
        weights = [ 0.20, 0.80 ]
        df = blend_single_formation(q_ends, u_samples, formations, weights)
        self.assertEqual(len(df.index), 13)

        # TODO Cellforce: check the asserted LadungMAhg values
        self.assertAlmostEqual(df['SpannungV'].iloc[0], 4.00)
        self.assertAlmostEqual(df['LadungMAhg'].iloc[0], 1.00) # = 1.00 * 0.20 + 1.00 * 0.80
        
        self.assertAlmostEqual(df['SpannungV'].iloc[1], 3.75)
        self.assertAlmostEqual(df['LadungMAhg'].iloc[1], 1.30) # = 1.50 * 0.20 + 1.25 * 0.80
        
        self.assertAlmostEqual(df['SpannungV'].iloc[2], 3.50)
        self.assertAlmostEqual(df['LadungMAhg'].iloc[2], 1.60) # = 2.00 * 0.20 + 1.50 * 0.80
        
        self.assertAlmostEqual(df['SpannungV'].iloc[3], 3.25)
        self.assertAlmostEqual(df['LadungMAhg'].iloc[3], 1.9) # = 2.50 * 0.20 + 1.75 * 0.80
        
        self.assertAlmostEqual(df['SpannungV'].iloc[4], 3.00)
        self.assertAlmostEqual(df['LadungMAhg'].iloc[4], 2.20) # = 3.00 * 0.20 + 2.00 * 0.80
        
        self.assertAlmostEqual(df['SpannungV'].iloc[5], 2.75)
        self.assertAlmostEqual(df['LadungMAhg'].iloc[5], 3.10) # = 3.50 * 0.20 + 3.00 * 0.80
        
        self.assertAlmostEqual(df['SpannungV'].iloc[6], 2.50)
        self.assertAlmostEqual(df['LadungMAhg'].iloc[6], 4.00) # = 4.00 * 0.20 + 4.00 * 0.80
        
        self.assertAlmostEqual(df['SpannungV'].iloc[7], 2.25)
        self.assertAlmostEqual(df['LadungMAhg'].iloc[7], 4.90) # = 4.50 * 0.20 + 5.00 * 0.80
        
        self.assertAlmostEqual(df['SpannungV'].iloc[8], 2.00)
        self.assertAlmostEqual(df['LadungMAhg'].iloc[8], 5.80) # = 5.00 * 0.20 + 6.00 * 0.80
        
        self.assertAlmostEqual(df['SpannungV'].iloc[9], 1.75)
        self.assertAlmostEqual(df['LadungMAhg'].iloc[9], 6.70) # = 5.50 * 0.20 + 7.00 * 0.80
        
        self.assertAlmostEqual(df['SpannungV'].iloc[10], 1.50)
        self.assertAlmostEqual(df['LadungMAhg'].iloc[10], 7.60) # = 6.00 * 0.20 + 8.00 * 0.80
        
        self.assertAlmostEqual(df['SpannungV'].iloc[11], 1.25)
        self.assertAlmostEqual(df['LadungMAhg'].iloc[11], 8.50) # = 6.50 * 0.20 + 9.00 * 0.80
        
        self.assertAlmostEqual(df['SpannungV'].iloc[12], 1.00)
        self.assertAlmostEqual(df['LadungMAhg'].iloc[12], 9.40) # = 7.00 * 0.20 + 10.00 * 0.80

    def test_blend_single_formation_decreasing_multiple_samples_interpolated_q_end_clipped(self):
        q_ends = [ 3.00, 8.00 ]
        # q is increasing --> only q values larger than q_end are considered

        u_samples = pd.Series([ 4.00, 3.75, 3.50, 3.25, 3.00, 2.75, 2.50, 2.25, 2.00, 1.75, 1.50, 1.25, 1.00 ])
        formations = [ Formation('', test_cycle_q_increasing_u_decreasing_1), Formation('', test_cycle_q_increasing_u_decreasing_2) ]
        weights = [ 0.20, 0.80 ]
        df = blend_single_formation(q_ends, u_samples, formations, weights)
        self.assertEqual(len(df.index), 13)

        # TODO Cellforce: check the asserted LadungMAhg values
        self.assertAlmostEqual(df['SpannungV'].iloc[0], 4.00)
        self.assertAlmostEqual(df['LadungMAhg'].iloc[0], 7.00) # = 3.00 (extrapolated) * 0.20 + 8.00 (extrapolated) * 0.80
        
        self.assertAlmostEqual(df['SpannungV'].iloc[1], 3.75)
        self.assertAlmostEqual(df['LadungMAhg'].iloc[1], 7.00) # = 3.00 (extrapolated) * 0.20 + 8.00 (extrapolated) * 0.80
        
        self.assertAlmostEqual(df['SpannungV'].iloc[2], 3.50)
        self.assertAlmostEqual(df['LadungMAhg'].iloc[2], 7.00) # = 3.00 (extrapolated) * 0.20 + 8.00 (extrapolated) * 0.80
        
        self.assertAlmostEqual(df['SpannungV'].iloc[3], 3.25)
        self.assertAlmostEqual(df['LadungMAhg'].iloc[3], 7.00) # = 3.00 (extrapolated) * 0.20 + 8.00 (extrapolated) * 0.80
        
        self.assertAlmostEqual(df['SpannungV'].iloc[4], 3.00)
        self.assertAlmostEqual(df['LadungMAhg'].iloc[4], 7.00) # = 3.00 * 0.20 + 8.00 (extrapolated) * 0.80
        
        self.assertAlmostEqual(df['SpannungV'].iloc[5], 2.75)
        self.assertAlmostEqual(df['LadungMAhg'].iloc[5], 7.10) # = 3.50 * 0.20 + 8.00 (extrapolated) * 0.80
        
        self.assertAlmostEqual(df['SpannungV'].iloc[6], 2.50)
        self.assertAlmostEqual(df['LadungMAhg'].iloc[6], 7.20) # = 4.00 * 0.20 + 8.00 (extrapolated) * 0.80
        
        self.assertAlmostEqual(df['SpannungV'].iloc[7], 2.25)
        self.assertAlmostEqual(df['LadungMAhg'].iloc[7], 7.30) # = 4.50 * 0.20 + 8.00 (extrapolated) * 0.80
        
        self.assertAlmostEqual(df['SpannungV'].iloc[8], 2.00)
        self.assertAlmostEqual(df['LadungMAhg'].iloc[8], 7.40) # = 5.00 * 0.20 + 8.00 (extrapolated) * 0.80
        
        self.assertAlmostEqual(df['SpannungV'].iloc[9], 1.75)
        self.assertAlmostEqual(df['LadungMAhg'].iloc[9], 7.50) # = 5.50 * 0.20 + 8.00 (extrapolated) * 0.80
        
        self.assertAlmostEqual(df['SpannungV'].iloc[10], 1.50)
        self.assertAlmostEqual(df['LadungMAhg'].iloc[10], 7.60) # = 6.00 * 0.20 + 8.00 * 0.80
        
        self.assertAlmostEqual(df['SpannungV'].iloc[11], 1.25)
        self.assertAlmostEqual(df['LadungMAhg'].iloc[11], 8.50) # = 6.50 * 0.20 + 9.00 * 0.80
        
        self.assertAlmostEqual(df['SpannungV'].iloc[12], 1.00)
        self.assertAlmostEqual(df['LadungMAhg'].iloc[12], 9.40) # = 7.00 * 0.20 + 10.00 * 0.80

if __name__ == '__main__':
    unittest.main()
