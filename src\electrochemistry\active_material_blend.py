from typing import Optional
from electrochemistry.active_material import ActiveMaterialType, ActiveMaterialWeight
from electrochemistry.blending import FormationWeight, calculate_blend_formations
from electrochemistry.formation_table import FormationTable
from electrochemistry.material import MaterialWeight
from electrochemistry.prelithiation import Prelithiation
from models.chart_model import ChartModel

class ActiveMaterialBlend:

    active_materials: list[ActiveMaterialWeight]
    materials: list[MaterialWeight]
    formations: FormationTable

    def __init__(self, active_materials: list[ActiveMaterialWeight], formations: FormationTable, q_aim: Optional[float] = None, q_aim_first_charge: Optional[float] = None, prelithiation: Optional[Prelithiation] = None):

        self.q_aim = q_aim
        self.q_aim_first_charge = q_aim_first_charge
        # normalize weights
        weight_sum = sum(m.weight for m in active_materials)
        active_materials = [ActiveMaterialWeight(m.material, m.weight / weight_sum) for m in active_materials]

        self.formations = formations
        if self.formations is None:
            self.formations = calculate_blend_formations([FormationWeight(m.material.formations, m.weight) for m in active_materials])
            
        if q_aim is not None:
            factor = q_aim / self.formations.get_formation_c_10_discharge().capacity
            self.formations = self.formations.scale_q(factor)

        if q_aim_first_charge is not None:
            factor = q_aim_first_charge / self.formations.get_formation_first_charge().capacity
            offset = self.formations.get_formation_first_charge().capacity * factor - self.formations.get_formation_first_charge().capacity
            self.formations = self.formations.with_formation_first_charge(self.formations.get_formation_first_charge().scale_q(factor))
            self.formations = FormationTable([(f.translate_q(offset) if f.description != 'Formierung 1. Laden' else f) for f in self.formations.formations])
            # translate q

        self.prelithiation = prelithiation
        if self.prelithiation is not None:
            self.formations = self.formations.apply_prelithiation(self.prelithiation)

        self.active_materials = active_materials

        # unwind materials
        self.materials = [MaterialWeight(material = component.material, weight = material.weight * component.weight) for material in self.active_materials for component in material.material.composition]

    def get_active_surface(self) -> float:
        return sum(m.weight * m.material.active_surface for m in self.active_materials if m.material.active_surface is not None)

    def get_chart_data(self, n_samples: int) -> ChartModel:
        if len(self.materials) == 0:
            return None
        else:
            formation_tables = { f'material{index + 1}': (m.material.name, m.material.formations) for (index, m) in enumerate(self.active_materials) }
            if len(self.active_materials) > 1:
                formation_tables = { 'blend': ('Blend', self.formations) } | formation_tables
            elif self.q_aim is not None or self.q_aim_first_charge is not None:
                formation_tables = { 'Modified value by target': ('Modified value by target', self.formations) } | formation_tables
            return FormationTable.get_prefixed_chart_data(formation_tables, n_samples)

    def get_summary_table(self, u_min: float | None, u_max: float | None) -> dict:
        if len(self.active_materials) == 0:
            return None
        else:
            formation_tables = { m.material.name: m.material.formations for m in self.active_materials }
            if len(self.active_materials) > 1:
                formation_tables = { 'Blend': self.formations } | formation_tables
            elif self.q_aim is not None or self.q_aim_first_charge is not None:
                formation_tables = { 'Modified value by target': self.formations } | formation_tables
            return FormationTable.get_prefixed_summary_table(
                formation_tables,
                u_min = u_min,
                u_max = u_max
            )

    @property
    def composite_density(self) -> float:
        return sum(m.weight for m in self.active_materials) / sum(m.weight / m.material.density for m in self.active_materials)
