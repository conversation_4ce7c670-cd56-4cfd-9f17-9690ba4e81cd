from dataclasses import dataclass
import math
import numbers

from bson import ObjectId
from auth import bearer
import io
import os
import re
from typing import Callable, Iterable, Optional, Union
from flask import Blueprint, request
import jsons
from cell_design_metrics_blueprint import calculate
from electrochemistry.cell_format import CellFormatPouch, CellFormatPrisma
from electrochemistry.factory import create_full_cell
from electrochemistry.full_cell import FullCell
from electrochemistry.material import MaterialType
from models.cell_design_model import CellDesignModel, CellDesignModelWithId
import zipfile
import openpyxl
from openpyxl import load_workbook
from openpyxl.writer.excel import ExcelWriter
from openpyxl.styles import PatternFill, Border, Side, Alignment, Font
from openpyxl.utils import get_column_letter
from jsons_config import jsons_flask_fork, jsons_mongodb_fork
from database import database
from cell_design_blueprint import _pipeline, stringify_optional_fields
from electrochemistry.serialization import CellFormatCylinderProperties, CellFormatPouchProperties, CellFormatPrismaProperties, CellFormatProperties, CellGeometry
from settingslocal import VERSION
from models.cell_design_export_model import CellDesignExportModel

cell_design_export_blueprint = Blueprint('cell_design_export_blueprint', __name__, url_prefix='/api/cell-design-export')

def camelcase_to_snakecase(str_: str) -> str:
    return str_[0].lower() + re.sub(r'([A-Z])', '_\\1', str_[1:]).lower()

@cell_design_export_blueprint.post('/excel/')
@bearer.secure('user')
def generate_excel_export():
    body = request.get_json()
    cell_design = jsons.load(body, Union[CellDesignModelWithId, CellDesignModel], fork_inst = jsons_flask_fork, key_transformer=camelcase_to_snakecase)
    return generate_excel([cell_design]), { 'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }

@cell_design_export_blueprint.post('/json/')
@bearer.secure('user')
def generate_json_export():
    body = request.get_json()
    cell_design = jsons.load(body, Union[CellDesignModelWithId, CellDesignModel], fork_inst = jsons_flask_fork, key_transformer=camelcase_to_snakecase)
    metrics = calculate(cell_design)
    export = CellDesignExportModel(cell_design = cell_design, cell_design_metrics = metrics)

    return jsons.dump(export, CellDesignExportModel, fork_inst = jsons_flask_fork, key_transformer=jsons.KEY_TRANSFORMER_CAMELCASE)

@cell_design_export_blueprint.post('/excel/multiple')
@bearer.secure('user')
def generate_excel_export_multiple():
    body = request.get_json()
    object_ids = jsons.load(body, list[ObjectId], fork_inst = jsons_flask_fork, key_transformer=camelcase_to_snakecase)
    cell_design_documents = database['cell-designs'].aggregate([{ '$match': { '_id': { '$in': object_ids } } }] + _pipeline)
    cell_design_models = [jsons.load(stringify_optional_fields(document), CellDesignModelWithId, fork_inst = jsons_flask_fork, key_transformer = camelcase_to_snakecase, transform_dict_keys = False) for document in cell_design_documents]
    cell_design_lookup = {model._id: model for model in cell_design_models}
    cell_designs = [cell_design_lookup[id] for id in object_ids]
    return generate_excel(cell_designs), { 'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }

@dataclass
class ChapterItem:
    name: str
    unit: str
    fn: Callable[[CellDesignModel, FullCell], Union[str, float]]
    number_format: Optional[str] = None
    fill_color: Optional[str] = None
    font_color: Optional[str] = None
    bold: Optional[bool] = None

@dataclass
class Chapter:
    header: str
    items: list[Union['Chapter', ChapterItem]]
    number_format: Optional[str] = None
    fill_color: Optional[str] = None
    font_color: Optional[str] = None
    bold: Optional[bool] = None

def create_chapters() -> list[Chapter]:

    ### The ones that are commented differ from the values in the Matlab app ###
    ### Also, all of the values for Kathode Tab and Anode Tab are swapped ###
    chapters: list[Chapter] = [
        Chapter(
            header = 'Zusammenfassung',
            fill_color = '00B0F0',
            font_color = 'FFFFFF',
            bold = True,
            items = [
                ChapterItem(name = 'Kathodenmaterial', unit = '', fn = lambda design, full_cell: ' + '.join(f'{material.weight} : {material.material.name}' for material in full_cell.cell_layer.cathode.active_material.materials)),
                ChapterItem(name = 'Anodenmaterial', unit = '', fn = lambda design, full_cell: ' + '.join(f'{material.weight} : {material.material.name}' for material in full_cell.cell_layer.anode.active_material.materials)),
                ChapterItem(name = 'Prälithiierung', unit = 'mAh/g', number_format = '0', fn = lambda design, full_cell: design.prelithiation_capacity ),
                ChapterItem(name = 'Untere Spannungsgrenze', number_format = '0.00', unit = 'V', fn = lambda design, full_cell: full_cell.balancing.u_min),
                ChapterItem(name = 'Obere Spannungsgrenze', number_format = '0.00', unit = 'V', fn = lambda design, full_cell: full_cell.balancing.u_max),
                ChapterItem(name = 'Nominelle Spannung (C/10)', number_format = '0.00', unit = 'V', fn = lambda design, full_cell: full_cell.u_nom_c10),
                ChapterItem(name = 'Zellformat', unit = '', fn = lambda design, full_cell: design.cell_format_id),
                ChapterItem(name = 'Kathodenlagen', unit = '', number_format = '0', fn = lambda design, full_cell: full_cell.cell_format.no_cathode_layers),
                ChapterItem(name = 'Anodenlagen', unit = '', number_format = '0', fn = lambda design, full_cell: full_cell.cell_format.no_anode_layers),
                ChapterItem(name = 'Kapazität (C/10)', unit = 'Ah', number_format = '0.0', fn = lambda design, full_cell: full_cell.safe_capacity_c10),
                ChapterItem(name = 'Energie (C/10)', unit = 'Wh', number_format = '0', fn = lambda design, full_cell: full_cell.safe_energy_c10),
                ChapterItem(name = 'Gravimetrische Energiedichte (C/10)', unit = 'Wh/kg', number_format = '0', fn = lambda design, full_cell: full_cell.safe_energy_density_kg_c10),
                ChapterItem(name = 'Volumetrische Energiedichte (C/10)', unit = 'Wh/l', number_format = '0', fn = lambda design, full_cell: full_cell.safe_energy_density_l_c10),
                ChapterItem(name = 'Gewicht', unit = 'g', number_format = '0', fn = lambda design, full_cell: full_cell.weight),
                ChapterItem(name = 'Volumen', unit = 'l', number_format = '0.000', fn = lambda design, full_cell: full_cell.volume),
                ChapterItem(name = 'Preis (C/10)', unit = '€/kWh', number_format = '_-* #,##0.00 €_-;-* #,##0.00 €_-;_-* "-"?? €_-;_-@_-', fn = lambda design, full_cell: full_cell.price_kwh_c10)
               
            ]
        ),
        Chapter(
            header = 'Kathode',
            items = [
                ChapterItem(name = 'Aktivmaterial 1', unit = '', bold = True, fn = lambda design, full_cell: full_cell.cell_layer.cathode.active_material.active_materials[0].material.name),
                ChapterItem(name = 'First Cycle Kapazität C/10', unit = 'mAh/g', number_format = '0', fn = lambda design, full_cell: full_cell.cell_layer.cathode.active_material.active_materials[0].material.formations.formations[0].capacity),
                ChapterItem(name = 'Coulomb Effizienz Formierung', unit = '%', number_format = '0.0%', fn = lambda design, full_cell: full_cell.cell_layer.cathode.active_material.active_materials[0].material.formations.get_coulomb_efficiency()),
                ChapterItem(name = 'Kapazität C/10', unit = 'mAh/g', number_format = '0', fn = lambda design, full_cell: full_cell.cell_layer.cathode.active_material.active_materials[0].material.formations.get_formation_c_10_discharge().capacity),
                ChapterItem(name = 'Kapazität C/3', unit = 'mAh/g', number_format = '0', fn = lambda design, full_cell: full_cell.cell_layer.cathode.active_material.active_materials[0].material.formations.get_formation_c_3_discharge().capacity),
                ChapterItem(name = 'Kapazität C/2', unit = 'mAh/g', number_format = '0', fn = lambda design, full_cell: full_cell.cell_layer.cathode.active_material.active_materials[0].material.formations.get_formation_c_2_discharge().capacity),
                ChapterItem(name = 'Kapazität 1C', unit = 'mAh/g', number_format = '0', fn = lambda design, full_cell: full_cell.cell_layer.cathode.active_material.active_materials[0].material.formations.get_formation_1_c_discharge().capacity),

                ChapterItem(name = 'Aktivmaterial 2', unit = '', bold = True, fn = lambda design, full_cell: full_cell.cell_layer.cathode.active_material.active_materials[1].material.name if len(full_cell.cell_layer.cathode.active_material.active_materials) > 1 else None),
                ChapterItem(name = 'First Cycle Kapazität C/10', number_format = '0', unit = 'mAh/g', fn = lambda design, full_cell: full_cell.cell_layer.cathode.active_material.active_materials[1].material.formations.formations[0].capacity if len(full_cell.cell_layer.cathode.active_material.active_materials) > 1 else None),
                ChapterItem(name = 'Coulomb Effizienz Formierung', unit = '%', number_format = '0.0%', fn = lambda design, full_cell: full_cell.cell_layer.cathode.active_material.active_materials[1].material.formations.get_coulomb_efficiency() if len(full_cell.cell_layer.cathode.active_material.active_materials) > 1 else None),
                ChapterItem(name = 'Kapazität C/10', unit = 'mAh/g', number_format = '0', fn = lambda design, full_cell: full_cell.cell_layer.cathode.active_material.active_materials[1].material.formations.get_formation_c_10_discharge().capacity if len(full_cell.cell_layer.cathode.active_material.active_materials) > 1 else None),
                ChapterItem(name = 'Kapazität C/3', unit = 'mAh/g', number_format = '0', fn = lambda design, full_cell: full_cell.cell_layer.cathode.active_material.active_materials[1].material.formations.get_formation_c_3_discharge().capacity if len(full_cell.cell_layer.cathode.active_material.active_materials) > 1 else None),
                ChapterItem(name = 'Kapazität C/2', unit = 'mAh/g', number_format = '0', fn = lambda design, full_cell: full_cell.cell_layer.cathode.active_material.active_materials[1].material.formations.get_formation_c_2_discharge().capacity if len(full_cell.cell_layer.cathode.active_material.active_materials) > 1 else None),
                ChapterItem(name = 'Kapazität 1C', unit = 'mAh/g', number_format = '0', fn = lambda design, full_cell: full_cell.cell_layer.cathode.active_material.active_materials[1].material.formations.get_formation_1_c_discharge().capacity if len(full_cell.cell_layer.cathode.active_material.active_materials) > 1 else None),

                Chapter(
                    header = 'Rezept',
                    items = [
                        ChapterItem(name = 'Anteil Aktivmaterial 1', unit = '%', number_format = '0.00%', fn = lambda design, full_cell: full_cell.cell_layer.cathode.active_material.active_materials[0].weight * full_cell.cell_layer.cathode.active_material_weight),
                        ChapterItem(name = 'Anteil Aktivmaterial 2', unit = '%', number_format = '0.00%', fn = lambda design, full_cell: full_cell.cell_layer.cathode.active_material.active_materials[1].weight * full_cell.cell_layer.cathode.active_material_weight if len(full_cell.cell_layer.cathode.active_material.active_materials) > 1 else None),
                        ChapterItem(name = 'Prälithiierungsprozess', unit = '', fn = lambda design, full_cell: design.prelithiation_process),
                        ChapterItem(name = 'Lithiumanteil', unit = '%', number_format = '0.00%', fn = lambda design, full_cell: full_cell.cell_layer.cathode.lithium_weight),
                        ChapterItem(name = 'Bindername 1', unit = '', fn = lambda design, full_cell: full_cell.cell_layer.cathode.binder_materials[0].material.name if len(full_cell.cell_layer.cathode.binder_materials) > 0 else None),
                        ChapterItem(name = 'Anteil Binder 1', unit = '%', number_format = '0.00%', fn = lambda design, full_cell: full_cell.cell_layer.cathode.binder_materials[0].weight if len(full_cell.cell_layer.cathode.binder_materials) > 0 else None),
                        ChapterItem(name = 'Bindername 2', unit = '', fn = lambda design, full_cell: full_cell.cell_layer.cathode.binder_materials[1].material.name if len(full_cell.cell_layer.cathode.binder_materials) > 1 else None),
                        ChapterItem(name = 'Anteil Binder 2', unit = '%', number_format = '0.00%', fn = lambda design, full_cell: full_cell.cell_layer.cathode.binder_materials[1].weight if len(full_cell.cell_layer.cathode.binder_materials) > 1 else None),
                        ChapterItem(name = 'Bindername 3', unit = '', fn = lambda design, full_cell: full_cell.cell_layer.cathode.binder_materials[2].material.name if len(full_cell.cell_layer.cathode.binder_materials) > 2 else None),
                        ChapterItem(name = 'Anteil Binder 3', unit = '%', number_format = '0.00%', fn = lambda design, full_cell: full_cell.cell_layer.cathode.binder_materials[2].weight if len(full_cell.cell_layer.cathode.binder_materials) > 2 else None),
                        ChapterItem(name = 'Leitrussname', unit = '', fn = lambda design, full_cell: next((m.material.name for m in full_cell.cell_layer.cathode.conductive_additive_materials if m.material.id == 'leitruss_c65'), None)),
                        ChapterItem(name = 'Anteil Leitruss', unit = '%', number_format = '0.00%', fn = lambda design, full_cell:  next((m.weight for m in full_cell.cell_layer.cathode.conductive_additive_materials if m.material.id == 'leitruss_c65'), None)),
                        ChapterItem(name = 'CNT Name', unit = '', fn = lambda design, full_cell: next((m.material.name for m in full_cell.cell_layer.cathode.conductive_additive_materials if m.material.id == 'cnt'), None)),
                        ChapterItem(name = 'Anteil CNT', unit = '%', number_format = '0.00%', fn = lambda design, full_cell: next((m.weight for m in full_cell.cell_layer.cathode.conductive_additive_materials if m.material.id == 'cnt'), None)),
                        ChapterItem(name = 'Theoretische Dichte Aktivmaterial 1', unit = 'g/cm³', number_format = '0.00', fn = lambda design, full_cell: full_cell.cell_layer.cathode.active_material.materials[0].material.density),
                        ChapterItem(name = 'Theoretische Dichte Aktivmaterial 2', unit = 'g/cm³', number_format = '0.00', fn = lambda design, full_cell: full_cell.cell_layer.cathode.active_material.materials[1].material.density if len(full_cell.cell_layer.cathode.active_material.materials) > 1 else None),
                        ChapterItem(name = 'Theoretische Dichte Binder', unit = 'g/cm³', number_format = '0.00', fn = lambda design, full_cell: full_cell.cell_layer.cathode.binder_materials[0].material.density if len(full_cell.cell_layer.cathode.binder_materials) == 1 else None),
                        ChapterItem(name = 'Theoretische Dichte Leitruss', unit = 'g/cm³', number_format = '0.00', fn = lambda design, full_cell: next((m.material.density for m in full_cell.cell_layer.cathode.conductive_additive_materials if m.material.id == 'leitruss_c65'), None)),
                        ChapterItem(name = 'Theoretische Dichte CNT', unit = 'g/cm³', number_format = '0.00', fn = lambda design, full_cell: next((m.material.density for m in full_cell.cell_layer.cathode.conductive_additive_materials if m.material.id == 'cnt'), None)),
                        ChapterItem(name = 'Theoretische Dichte Kathode', unit = 'g/cm³', number_format = '0.00', fn = lambda design, full_cell: full_cell.cell_layer.cathode.get_composite_density()),
                        ChapterItem(name = 'First Cycle Kapazität C/10', unit = 'mAh/g', number_format = '0', fn = lambda design, full_cell: full_cell.cell_layer.cathode.active_material.formations.formations[0].capacity),
                        ChapterItem(name = 'Coulomb Effizienz Formierung', unit = '%', number_format = '0.0%', fn = lambda design, full_cell: full_cell.cell_layer.cathode.active_material.formations.get_coulomb_efficiency()),
                        ChapterItem(name = 'Kapazität C/10', unit = 'mAh/g', number_format = '0', bold = True, fn = lambda design, full_cell: full_cell.cell_layer.cathode.active_material.formations.get_formation_c_10_discharge().capacity),
                        ChapterItem(name = 'Kapazität C/3', unit = 'mAh/g', number_format = '0', bold = True, fn = lambda design, full_cell: full_cell.cell_layer.cathode.active_material.formations.get_formation_c_3_discharge().capacity),
                        ChapterItem(name = 'Kapazität C/2', unit = 'mAh/g', number_format = '0', fn = lambda design, full_cell: full_cell.cell_layer.cathode.active_material.formations.get_formation_c_2_discharge().capacity),
                        ChapterItem(name = 'Kapazität 1C', unit = 'mAh/g', number_format = '0', fn = lambda design, full_cell: full_cell.cell_layer.cathode.active_material.formations.get_formation_1_c_discharge().capacity)
                    ]
                ),
            ]
        ),
        Chapter(
            header = 'Anode',
            items = [
                ChapterItem(name = 'Aktivmaterial 2', unit = '', bold = True, fn = lambda design, full_cell: full_cell.cell_layer.anode.active_material.materials[0].material.name),
                ChapterItem(name = 'First Cycle Kapazität C/10', unit = 'mAh/g', number_format = '0', fn = lambda design, full_cell: full_cell.cell_layer.anode.active_material.active_materials[0].material.formations.formations[0].capacity),
                ChapterItem(name = 'Coulomb Effizienz Formierung', unit = '%', number_format = '0.0%', fn = lambda design, full_cell: full_cell.cell_layer.anode.active_material.active_materials[0].material.formations.get_coulomb_efficiency()),
                ChapterItem(name = 'Kapazität C/10', unit = 'mAh/g', number_format = '0', fn = lambda design, full_cell: full_cell.cell_layer.anode.active_material.active_materials[0].material.formations.get_formation_c_10_discharge().capacity),
                ChapterItem(name = 'Kapazität C/3', unit = 'mAh/g', number_format = '0', fn = lambda design, full_cell: full_cell.cell_layer.anode.active_material.active_materials[0].material.formations.get_formation_c_3_discharge().capacity),
                ChapterItem(name = 'Kapazität C/2', unit = 'mAh/g', number_format = '0', fn = lambda design, full_cell: full_cell.cell_layer.anode.active_material.active_materials[0].material.formations.get_formation_c_2_discharge().capacity),
                ChapterItem(name = 'Kapazität 1C', unit = 'mAh/g', number_format = '0', fn = lambda design, full_cell: full_cell.cell_layer.anode.active_material.active_materials[0].material.formations.get_formation_1_c_discharge().capacity),

                ChapterItem(name = 'Aktivmaterial 2', unit = '', bold = True, fn = lambda design, full_cell: full_cell.cell_layer.anode.active_material.materials[1].material.name if len(full_cell.cell_layer.anode.active_material.active_materials) > 1 else None),
                ChapterItem(name = 'First Cycle Kapazität C/10', number_format = '0', unit = 'mAh/g', fn = lambda design, full_cell: full_cell.cell_layer.anode.active_material.active_materials[1].material.formations.formations[0].capacity if len(full_cell.cell_layer.anode.active_material.active_materials) > 1 else None),
                ChapterItem(name = 'Coulomb Effizienz Formierung', number_format = '0.0%', unit = '%', fn = lambda design, full_cell: full_cell.cell_layer.anode.active_material.active_materials[1].material.formations.get_coulomb_efficiency() if len(full_cell.cell_layer.anode.active_material.active_materials) > 1 else None),
                ChapterItem(name = 'Kapazität C/10', unit = 'mAh/g', number_format = '0', fn = lambda design, full_cell: full_cell.cell_layer.anode.active_material.active_materials[1].material.formations.get_formation_c_10_discharge().capacity if len(full_cell.cell_layer.anode.active_material.active_materials) > 1 else None),
                ChapterItem(name = 'Kapazität C/3', unit = 'mAh/g', number_format = '0', fn = lambda design, full_cell: full_cell.cell_layer.anode.active_material.active_materials[1].material.formations.get_formation_c_3_discharge().capacity if len(full_cell.cell_layer.anode.active_material.active_materials) > 1 else None),
                ChapterItem(name = 'Kapazität C/2', unit = 'mAh/g', number_format = '0', fn = lambda design, full_cell: full_cell.cell_layer.anode.active_material.active_materials[1].material.formations.get_formation_c_2_discharge().capacity if len(full_cell.cell_layer.anode.active_material.active_materials) > 1 else None),
                ChapterItem(name = 'Kapazität 1C', unit = 'mAh/g', number_format = '0', fn = lambda design, full_cell: full_cell.cell_layer.anode.active_material.active_materials[1].material.formations.get_formation_1_c_discharge().capacity if len(full_cell.cell_layer.anode.active_material.active_materials) > 1 else None),

                Chapter(
                    header = 'Rezept',
                    items = [
                        ChapterItem(name = 'Anteil Aktivmaterial 1', unit = '%', number_format = '0.00%', fn = lambda design, full_cell: full_cell.cell_layer.anode.active_material.active_materials[0].weight * full_cell.cell_layer.anode.active_material_weight),
                        ChapterItem(name = 'Anteil Aktivmaterial 2', unit = '%', number_format = '0.00%', fn = lambda design, full_cell: full_cell.cell_layer.anode.active_material.active_materials[1].weight * full_cell.cell_layer.anode.active_material_weight if len(full_cell.cell_layer.anode.active_material.active_materials) > 1 else None),
                        ChapterItem(name = 'Bindername 1', unit = '', fn = lambda design, full_cell: full_cell.cell_layer.anode.binder_materials[0].material.name if len(full_cell.cell_layer.anode.binder_materials) > 0 else None),
                        ChapterItem(name = 'Anteil Binder 1', unit = '%', number_format = '0.00%', fn = lambda design, full_cell: full_cell.cell_layer.anode.binder_materials[0].weight if len(full_cell.cell_layer.anode.binder_materials) > 0 else None),
                        ChapterItem(name = 'Bindername 2', unit = '', fn = lambda design, full_cell: full_cell.cell_layer.anode.binder_materials[1].material.name if len(full_cell.cell_layer.anode.binder_materials) > 1 else None),
                        ChapterItem(name = 'Anteil Binder 2', unit = '%', number_format = '0.00%', fn = lambda design, full_cell: full_cell.cell_layer.anode.binder_materials[1].weight if len(full_cell.cell_layer.anode.binder_materials) > 1 else None),
                        ChapterItem(name = 'Bindername 3', unit = '', fn = lambda design, full_cell: full_cell.cell_layer.anode.binder_materials[2].material.name if len(full_cell.cell_layer.anode.binder_materials) > 2 else None),
                        ChapterItem(name = 'Anteil Binder 3', unit = '%', number_format = '0.00%', fn = lambda design, full_cell: full_cell.cell_layer.anode.binder_materials[2].weight if len(full_cell.cell_layer.anode.binder_materials) > 2 else None),
                        ChapterItem(name = 'Leitrussname', unit = '', fn = lambda design, full_cell: next((m.material.name for m in full_cell.cell_layer.anode.conductive_additive_materials if m.material.id == 'leitruss_c65'), None)),
                        ChapterItem(name = 'Anteil Leitruss', unit = '%', number_format = '0.00%', fn = lambda design, full_cell:  next((m.weight for m in full_cell.cell_layer.anode.conductive_additive_materials if m.material.id == 'leitruss_c65'), None)),
                        ChapterItem(name = 'CNT Name', unit = '', fn = lambda design, full_cell: next((m.material.name for m in full_cell.cell_layer.anode.conductive_additive_materials if m.material.id == 'cnt'), None)),
                        ChapterItem(name = 'Anteil CNT', unit = '%', number_format = '0.00%', fn = lambda design, full_cell: next((m.weight for m in full_cell.cell_layer.anode.conductive_additive_materials if m.material.id == 'cnt'), None)),
                        ChapterItem(name = 'Theoretische Dichte Aktivmaterial1', unit = 'g/cm³', number_format = '0.00', fn = lambda design, full_cell: full_cell.cell_layer.anode.active_material.active_materials[0].material.density),
                        ChapterItem(name = 'Theoretische Dichte Aktivmaterial2', unit = 'g/cm³', number_format = '0.00', fn = lambda design, full_cell: full_cell.cell_layer.anode.active_material.active_materials[1].material.density if len(full_cell.cell_layer.anode.active_material.active_materials) > 1 else None),
                        ChapterItem(name = 'Theoretische Dichte Binder', unit = 'g/cm³', number_format = '0.00', fn = lambda design, full_cell: full_cell.cell_layer.anode.binder_materials[0].material.density if len(full_cell.cell_layer.anode.binder_materials) == 1 else None),
                        ChapterItem(name = 'Theoretische Dichte Leitruss', unit = 'g/cm³', number_format = '0.00', fn = lambda design, full_cell: next((m.material.density for m in full_cell.cell_layer.anode.conductive_additive_materials if m.material.id == 'leitruss_c65'), None)),
                        ChapterItem(name = 'Theoretische Dichte CNT', unit = 'g/cm³', number_format = '0.00', fn = lambda design, full_cell: next((m.material.density for m in full_cell.cell_layer.anode.conductive_additive_materials if m.material.id == 'cnt'), None)),
                        ChapterItem(name = 'Theoretische Dichte Anode', unit = 'g/cm³', number_format = '0.00', fn = lambda design, full_cell: full_cell.cell_layer.anode.get_composite_density()),
                        ChapterItem(name = 'Prälithiierung', unit = 'mAh/g', number_format = '0', bold = True, fn = lambda design, full_cell: design.prelithiation_capacity),
                        ChapterItem(name = 'First Cycle Kapazität C/10', unit = 'mAh/g', number_format = '0', fn = lambda design, full_cell: full_cell.cell_layer.anode.active_material.formations.formations[0].capacity),
                        ChapterItem(name = 'Coulomb Effizienz Formierung', unit = '%', number_format = '0.0%', fn = lambda design, full_cell: full_cell.cell_layer.anode.active_material.formations.get_coulomb_efficiency()),
                        ChapterItem(name = 'Kapazität C/10', unit = 'mAh/g', number_format = '0', bold = True, fn = lambda design, full_cell: full_cell.cell_layer.anode.active_material.formations.get_formation_c_10_discharge().capacity),
                        ChapterItem(name = 'Kapazität C/3', unit = 'mAh/g', number_format = '0', bold = True, fn = lambda design, full_cell: full_cell.cell_layer.anode.active_material.formations.get_formation_c_3_discharge().capacity),
                        ChapterItem(name = 'Kapazität C/2', unit = 'mAh/g', number_format = '0', fn = lambda design, full_cell: full_cell.cell_layer.anode.active_material.formations.get_formation_c_2_discharge().capacity),
                        ChapterItem(name = 'Kapazität 1C', unit = 'mAh/g', number_format = '0', fn = lambda design, full_cell: full_cell.cell_layer.anode.active_material.formations.get_formation_1_c_discharge().capacity)
                    ]
                )
            ]
        ),
        Chapter(
            header = 'Balancing',
            items = [
                ChapterItem(name = 'N/P Verhältnis Formierung', unit = '', number_format = '0.00', fn = lambda design, full_cell: full_cell.balancing.np_ratio_first),
                ChapterItem(name = 'N/P Verhältnis Reversibel', unit = '', number_format = '0.00', fn = lambda design, full_cell: full_cell.balancing.get_np_ratio_rev()),
                ChapterItem(name = 'Umin', unit = 'V', number_format = '0.00', fn = lambda design, full_cell: full_cell.balancing.u_min),
                ChapterItem(name = 'Umax', unit = 'V', number_format = '0.00', fn = lambda design, full_cell: full_cell.balancing.u_max),
                ChapterItem(name = 'Nennspannung C/10', unit = 'V', number_format = '0.00', bold = True, fn = lambda design, full_cell: full_cell.balancing.cell_formations.get_formation_c_10_discharge().u_nom),
                ChapterItem(name = 'Kapazität C/10', unit = 'mAh/g', number_format = '0', bold = True, fn = lambda design, full_cell: full_cell.balancing.cell_formations.get_formation_c_10_discharge().capacity),
                ChapterItem(name = 'Nennspannung C/3', unit = 'V', number_format = '0.00', bold = True, fn = lambda design, full_cell: full_cell.balancing.cell_formations.get_formation_c_3_discharge().u_nom),
                ChapterItem(name = 'Kapazität C/3', unit = 'mAh/g', number_format = '0', bold = True, fn = lambda design, full_cell: full_cell.balancing.cell_formations.get_formation_c_3_discharge().capacity)
            ]
        ),
        Chapter(
            header = 'Elektrolyt, Separator und Stromableiter',
            items = [
                ChapterItem(name = 'Elektrolytname', unit = '', fn = lambda design, full_cell: full_cell.electrolyte.material.name),
                ChapterItem(name = 'Elektrolytmenge', unit = 'mL/Ah', number_format = '0.00', fn = lambda design, full_cell: full_cell.electrolyte.amount),
                ChapterItem(name = 'Elektrolytswelling', unit = '%', number_format = '0.0%', fn = lambda design, full_cell: full_cell.electrolyte.swelling),
                ChapterItem(name = 'Dichte Elektrolyt', unit = 'g/cm³', number_format = '0.00', fn = lambda design, full_cell: full_cell.electrolyte.material.density),
                ChapterItem(name = 'Dichte Separator', unit = 'g/cm³', number_format = '0.00', fn = lambda design, full_cell: full_cell.cell_layer.separator.material.density),
                ChapterItem(name = 'Dichte Aluminium', unit = 'g/cm³', number_format = '0.00', fn = lambda design, full_cell: full_cell.cell_layer.al_cc.material.density),
                ChapterItem(name = 'Dichte Kupfer', unit = 'g/cm³', number_format = '0.00', fn = lambda design, full_cell: full_cell.cell_layer.cu_cc.material.density)
            ]
        ),
        Chapter(
            header = 'Beschichtungsflächendichte und Schichtdicken',
            items = [
                ChapterItem(name = 'Kalanderdichte Kathode', unit = 'g/cm³', number_format = '0.00', fn = lambda design, full_cell: design.cathode_calander_density),
                ChapterItem(name = 'Porosität Kathode', unit = '%', number_format = '0.0%', fn = lambda design, full_cell: full_cell.cell_layer.cathode.get_porosity() / 100),
                ChapterItem(name = 'Kalanderdichte Anode', unit = 'g/cm³', number_format = '0.00', fn = lambda design, full_cell: design.anode_calander_density),
                ChapterItem(name = 'Porosität Anode', unit = '%', number_format = '0.0%', fn = lambda design, full_cell: full_cell.cell_layer.anode.get_porosity() / 100),
                ChapterItem(name = 'Beschichtungsflächendichte Kathode', unit = 'mAh/cm²', number_format = '0.00', bold = True, fn = lambda design, full_cell: full_cell.cell_layer.cathode.areal_capacity),
                ChapterItem(name = 'Beschichtungsflächendichte Anode', unit = 'mAh/cm²', number_format = '0.00', bold = True, fn = lambda design, full_cell: full_cell.cell_layer.anode.areal_capacity),
                ChapterItem(name = 'Beladung Kathode', unit = 'mg/cm²', number_format = '0.00', fn = lambda design, full_cell: full_cell.cell_layer.cathode.loading),
                ChapterItem(name = 'Beladung Anode', unit = 'mg/cm²', number_format = '0.00', fn = lambda design, full_cell: full_cell.cell_layer.anode.loading),
                ChapterItem(name = 'Beschichtungsdicke Kathode', unit = 'µm', number_format = '0.00', fn = lambda design, full_cell: full_cell.cell_layer.cathode.coating_thickness),
                ChapterItem(name = 'Beschichtungsdicke Anode', unit = 'µm', number_format = '0.00', fn = lambda design, full_cell: full_cell.cell_layer.anode.coating_thickness),
                ChapterItem(name = 'Dicke Alufolie', unit = 'µm', number_format = '0.00', fn = lambda design, full_cell: full_cell.cell_layer.al_cc.thickness),
                ChapterItem(name = 'Dicke Kupferfolie', unit = 'µm', fn = lambda design, full_cell: full_cell.cell_layer.cu_cc.thickness),
                ChapterItem(name = 'Dicke Separator', unit = 'µm', number_format = '0.00', fn = lambda design, full_cell: full_cell.cell_layer.separator.thickness),
                ChapterItem(name = 'Dicke Zelllage', unit = 'µm', number_format = '0.00', bold = True, fn = lambda design, full_cell: full_cell.cell_layer.get_thickness()),
                ChapterItem(name = 'Dicke Kathode', unit = 'µm', number_format = '0.00', bold = True, fn = lambda design, full_cell: full_cell.cell_layer.cathode.get_thickness()),
                ChapterItem(name = 'Dicke Anode', unit = 'µm', number_format = '0.00', bold = True, fn = lambda design, full_cell: full_cell.cell_layer.anode.get_thickness())
            ]
        ),
        Chapter(
            header = 'Zellgeometrie',
            items = [
                ChapterItem(name = 'Zellformat', unit = '', bold = True, fn = lambda design, full_cell: design.cell_format_id),
                ChapterItem(name = 'Zelltyp', unit = '', fn = lambda design, full_cell: full_cell.cell_format.geometry),
                ChapterItem(name = 'Zell Länge / Zell Durchmesser', unit = 'mm', number_format = '0.0', fn = lambda design, full_cell: full_cell.cell_format.properties.cell_length if not isinstance(full_cell.cell_format.properties, CellFormatCylinderProperties) else full_cell.cell_format.properties.cell_diameter),
                ChapterItem(name = 'Zell Breite / Zell Höhe', unit = 'mm', number_format = '0.0', fn = lambda design, full_cell: full_cell.cell_format.properties.cell_width if not isinstance(full_cell.cell_format.properties, CellFormatCylinderProperties) else full_cell.cell_format.properties.cell_height),
                ChapterItem(name = 'Zell Dicke / Durchmesser Zellkern', unit = 'mm', number_format = '0.00', fn = lambda design, full_cell: full_cell.cell_format.properties.cell_thickness if not isinstance(full_cell.cell_format.properties, CellFormatCylinderProperties) else full_cell.cell_format.properties.cell_core_diameter),
                ChapterItem(name = 'Beschichtungslänge Kathode', unit = 'mm', number_format = '0.0', fn = lambda design, full_cell: full_cell.cell_format.cathode_coating_length),
                ChapterItem(name = 'Beschichtungsbreite Kathode', unit = 'mm', number_format = '0.0', fn = lambda design, full_cell: full_cell.cell_format.properties.coating_width_cathode),
                ChapterItem(name = 'Beschichtungsfläche Kathode', unit = 'mm²', number_format = '0.00', fn = lambda design, full_cell: full_cell.cell_format.cathode_coating_area),
                ChapterItem(name = 'Beschichtungslänge Anode', unit = 'mm', number_format = '0.0', fn = lambda design, full_cell: full_cell.cell_format.anode_coating_length),
                ChapterItem(name = 'Beschichtungsbreite Anode', unit = 'mm', number_format = '0.0', fn = lambda design, full_cell: full_cell.cell_format.properties.coating_width_anode),
                ChapterItem(name = 'Beschichtungsfläche Anode', unit = 'mm²', number_format = '0.00', fn = lambda design, full_cell: full_cell.cell_format.anode_coating_area),
                ChapterItem(name = 'Länge Separator', unit = 'mm', number_format = '0.0', fn = lambda design, full_cell: full_cell.cell_format.separator_coating_length),
                ChapterItem(name = 'Breite Separator', unit = 'mm', number_format = '0.0', fn = lambda design, full_cell: full_cell.cell_format.properties.coating_width_separator),
                ChapterItem(name = 'Fläche Separator', unit = 'mm²', number_format = '0.00', fn = lambda design, full_cell: full_cell.cell_format.separator_area),
                ChapterItem(name = 'Porosität Separator', unit = '%', number_format = '0.0%', fn = lambda design, full_cell: full_cell.separator_porosity / 100),
                ChapterItem(name = 'Porenvolumen gesamt', unit = 'mL', number_format = '0', fn = lambda design, full_cell: full_cell.pore_volume),
                ChapterItem(name = 'Porenvolumen pro Ah C/10', unit = 'mL/Ah', number_format = '0.00', fn = lambda design, full_cell: full_cell.pore_volume_ah)
            ]
        ),
        Chapter(
            header = 'Zelllagen',
            items = [
                ChapterItem(name = 'Pouch/Gehäuse-Wandstärke', unit = 'mm', number_format = '0.00', fn = lambda design, full_cell: full_cell.cell_format.housing_weight_with_tabs),
                ChapterItem(name = 'Elektrolytswelling', unit = 'mm', number_format = '0.00', fn = lambda design, full_cell: full_cell.electrolyte.swelling),
                ChapterItem(name = 'Swelling-Vorhalt', unit = 'mm', number_format = '0.00', fn = lambda design, full_cell: full_cell.cell_format.properties.swelling_buffer),
                ChapterItem(name = 'Montagefreiraum', unit = 'mm', number_format = '0.00', fn = lambda design, full_cell: full_cell.cell_format.assembly_clearance),
                ChapterItem(name = 'Max. Gesamtdicke Zelllagen', unit = 'mm', number_format = '0.00', fn = lambda design, full_cell: full_cell.cell_format.max_cell_layer_thickness),
                ChapterItem(name = 'Gesamtdicke Zelllagen', unit = 'mm', number_format = '0.00', fn = lambda design, full_cell: full_cell.cell_format.cell_layer_thickness),
                ChapterItem(name = 'Freiraum für Zelllagen', unit = 'µm', number_format = '0.00', fn = lambda design, full_cell: full_cell.cell_format.delta_cell_layer_thickness if isinstance(full_cell.cell_format, CellFormatPouch) or isinstance(full_cell.cell_format, CellFormatPrisma) else None),
                ChapterItem(name = 'Freiraum für Zelllagen / Dicke Zelllage', unit = '', number_format = '0.00', fn = lambda design, full_cell: full_cell.cell_format.mod_cell_layer_thickness if isinstance(full_cell.cell_format, CellFormatPouch) or isinstance(full_cell.cell_format, CellFormatPrisma) else None),
                ChapterItem(name = 'Anzahl Kathodenlagen', unit = '', number_format = '0', fn = lambda design, full_cell: full_cell.cell_format.no_cathode_layers),
                ChapterItem(name = 'Anzahl Anodenlagen', unit = '', number_format = '0', fn = lambda design, full_cell: full_cell.cell_format.no_anode_layers),
                ChapterItem(name = 'Anzahl Separatorlagen', unit = '', number_format = '0', fn = lambda design, full_cell: full_cell.cell_format.no_separator_layers),
                ChapterItem(name = 'Gesamtfläche Separator', unit = 'm²', number_format = '0.00', fn = lambda design, full_cell: full_cell.cell_format.separator_area),
                ChapterItem(name = 'Anzahl aktive Lagen', unit = '', number_format = '0', fn = lambda design, full_cell: full_cell.cell_format.no_cell_layers),
                ChapterItem(name = 'Kapazität pro Zelllage C/10', unit = 'Ah', number_format = '0.00', fn = lambda design, full_cell: full_cell.capacity_c10 / full_cell.cell_format.no_cathode_layers),
                ChapterItem(name = 'Energie pro Zelllage C/10', unit = 'Ah', number_format = '0.00', fn = lambda design, full_cell: full_cell.energy_c10 / full_cell.cell_format.no_cathode_layers),
                ChapterItem(name = 'Kapazität pro Zelllage C/3', unit = 'Ah', number_format = '0.00', fn = lambda design, full_cell: full_cell.capacity_c3 / full_cell.cell_format.no_cathode_layers),
                ChapterItem(name = 'Energie pro Zelllage C/3', unit = 'Wh', number_format = '0.00', fn = lambda design, full_cell: full_cell.energy_c3 / full_cell.cell_format.no_cathode_layers)
            ]
        ),
        Chapter(
            header = 'Zellgewicht',
            items = [
                ChapterItem(name = 'Kathode', unit = 'g', number_format = '0', bold = True, fn = lambda design, full_cell: full_cell.cathode_weight),
                ChapterItem(name = 'Aktivmaterial 1', unit = 'g', number_format = '0', fn = lambda design, full_cell: full_cell.cell_layer.cathode.active_material.materials[0].weight * full_cell.cell_layer.cathode.active_material_weight * full_cell.cathode_weight),
                ChapterItem(name = 'Aktivmaterial 2', unit = 'g', number_format = '0', fn = lambda design, full_cell: full_cell.cell_layer.cathode.active_material.materials[1].weight * full_cell.cell_layer.cathode.active_material_weight * full_cell.cathode_weight if len(full_cell.cell_layer.cathode.active_material.materials) > 1 else None),
                ChapterItem(name = 'Binder', unit = 'g', number_format = '0', fn = lambda design, full_cell: sum(m.weight for m in full_cell.cell_layer.cathode.binder_materials) * full_cell.cathode_weight),
                ChapterItem(name = 'Leitruss', unit = 'g', number_format = '0', fn = lambda design, full_cell: next((m.weight for m in full_cell.cell_layer.cathode.conductive_additive_materials if m.material.id == 'leitruss_c65'), 0) * full_cell.cathode_weight),
                ChapterItem(name = 'CNT', unit = 'g', number_format = '0', fn = lambda design, full_cell: next((m.weight for m in full_cell.cell_layer.cathode.conductive_additive_materials if m.material.id == 'cnt'), 0) * full_cell.cathode_weight),
                ChapterItem(name = 'Anode', unit = 'g', number_format = '0', bold = True, fn = lambda design, full_cell: full_cell.anode_weight),
                ChapterItem(name = 'Aktivmaterial 1', unit = 'g', number_format = '0', fn = lambda design, full_cell: full_cell.cell_layer.anode.active_material.materials[0].weight * full_cell.cell_layer.anode.active_material_weight * full_cell.anode_weight),
                ChapterItem(name = 'Aktivmaterial 2', unit = 'g', number_format = '0', fn = lambda design, full_cell: full_cell.cell_layer.anode.active_material.materials[1].weight * full_cell.cell_layer.anode.active_material_weight * full_cell.anode_weight if len(full_cell.cell_layer.anode.active_material.materials) > 1 else None),
                ChapterItem(name = 'Lithium aus Prälithiierung', unit = 'g', number_format = '0', fn = lambda design, full_cell: full_cell.cell_layer.anode.lithium_weight * full_cell.anode_weight),
                ChapterItem(name = 'Binder', unit = 'g', number_format = '0', fn = lambda design, full_cell: sum(m.weight for m in full_cell.cell_layer.anode.binder_materials) * full_cell.anode_weight),
                ChapterItem(name = 'Leitruss', unit = 'g', number_format = '0', fn = lambda design, full_cell: next((m.weight for m in full_cell.cell_layer.anode.conductive_additive_materials if m.material.id == 'leitruss_c65'), 0) * full_cell.anode_weight),
                ChapterItem(name = 'CNT', unit = 'g', number_format = '0', fn = lambda design, full_cell: next((m.weight for m in full_cell.cell_layer.anode.conductive_additive_materials if m.material.id == 'cnt'), 0) * full_cell.anode_weight),
                ChapterItem(name = 'Sonstiges', unit = '', bold = True, fn = lambda design, full_cell: ''),
                ChapterItem(name = 'Separator', unit = 'g', number_format = '0', fn = lambda design, full_cell: full_cell.cell_layer.separator.weight * full_cell.cell_format.no_separator_layers),
                ChapterItem(name = 'Aluminium Stromableiter', unit = 'g', number_format = '0', fn = lambda design, full_cell: full_cell.cell_layer.al_cc.weight * full_cell.cell_format.no_cathode_layers),
                ChapterItem(name = 'Kupfer Stromableiter', unit = 'g', number_format = '0', fn = lambda design, full_cell: full_cell.cell_layer.cu_cc.weight * full_cell.cell_format.no_anode_layers),
                ChapterItem(name = 'Elektrolyt', unit = 'g', number_format = '0', fn = lambda design, full_cell: full_cell.electrolyte.weight),
                ChapterItem(name = 'Kathoden Tab', unit = 'g', number_format = '0', fn = lambda design, full_cell: full_cell.cell_format.cathode_tab_weight),
                ChapterItem(name = 'Anoden Tab', unit = 'g', number_format = '0', fn = lambda design, full_cell: full_cell.cell_format.anode_tab_weight),
                ChapterItem(name = 'Pouchfolie/Gehäuse', unit = 'g', number_format = '0', fn = lambda design, full_cell: full_cell.cell_format.housing_weight),
                ChapterItem(name = 'Gewicht gesamt Zelle', unit = 'g', number_format = '0', bold = True, fn = lambda design, full_cell: full_cell.weight)
            ]
        ),
        Chapter(
            header = 'Kapazität und Energieinhalt',
            fill_color = '00B0F0',
            font_color = 'FFFFFF',
            bold = True,
            items = [
                ChapterItem(name = 'Gewicht gesamte Zelle', unit = 'g', number_format = '0', fn = lambda design, full_cell: full_cell.weight),
                ChapterItem(name = 'Volumen gesamte Zelle', unit = 'l', number_format = '0.000', fn = lambda design, full_cell: full_cell.volume),
                ChapterItem(name = 'Sicherheitsfaktor', unit = '', number_format = '0.00', fn = lambda design, full_cell: full_cell.safety)
            ]
        ),
        Chapter(
            header = 'C/10 Kapazität und Energie',
            fill_color = '00B0F0',
            font_color = 'FFFFFF',
            bold = True,
            items = [
                ChapterItem(name = 'Nominelle Spannung C/10', unit = 'V', number_format = '0.00', fn = lambda design, full_cell: full_cell.u_nom_c10),
                ChapterItem(name = 'Kapazität C/10', unit = 'Ah', number_format = '0.0', fn = lambda design, full_cell: full_cell.capacity_c10),
                ChapterItem(name = 'Energieinhalt C/10', unit = 'Wh', number_format = '0', fn = lambda design, full_cell: full_cell.energy_c10),
                ChapterItem(name = 'Energiedichte volumetrisch C/10', unit = 'Wh/l', number_format = '0', fn = lambda design, full_cell: full_cell.energy_density_l_c10),
                ChapterItem(name = 'Energiedichte gravimetrisch C/10', unit = 'Wh/kg', number_format = '0', fn = lambda design, full_cell: full_cell.energy_density_kg_c10)
            ]
        ),
        Chapter(
            header = 'C/10 Sicherheitsauslegung',
            fill_color = '00B0F0',
            font_color = 'FFFFFF',
            bold = True,
            items = [
                ChapterItem(name = 'Kapazität', unit = 'Ah', number_format = '0.0', fn = lambda design, full_cell: full_cell.safe_capacity_c10),
                ChapterItem(name = 'Energieinhalt', unit = 'Wh', number_format = '0', fn = lambda design, full_cell: full_cell.safe_energy_c10),
                ChapterItem(name = 'Energiedichte volumetrisch', unit = 'Wh/l', number_format = '0', fn = lambda design, full_cell: full_cell.safe_energy_density_l_c10),
                ChapterItem(name = 'Energiedichte gravimetrisch', unit = 'Wh/kg', number_format = '0', fn = lambda design, full_cell: full_cell.safe_energy_density_kg_c10)
            ]
        ),
        Chapter(
            header = 'C/3 Kapazität und Energie',
            fill_color = '3A3838',
            font_color = 'FFFFFF',
            bold = True,
            items = [
                ChapterItem(name = 'Nominelle Spannung C/3', unit = 'V', number_format = '0.00', fn = lambda design, full_cell: full_cell.u_nom_c3),
                ChapterItem(name = 'Kapazität C/3', unit = 'Ah', number_format = '0.0', fn = lambda design, full_cell: full_cell.capacity_c3),
                ChapterItem(name = 'Energieinhalt C/3', unit = 'Wh', number_format = '0', fn = lambda design, full_cell: full_cell.energy_c3),
                ChapterItem(name = 'Energiedichte volumetrisch C/3', unit = 'Wh/l', number_format = '0', fn = lambda design, full_cell: full_cell.energy_density_l_c3),
                ChapterItem(name = 'Energiedichte gravimetrisch C/3', unit = 'Wh/kg', number_format = '0', fn = lambda design, full_cell: full_cell.energy_density_kg_c3)
            ]
        ),
        Chapter(
            header = 'C/3 Sicherheitsauslegung',
            fill_color = '3A3838',
            font_color = 'FFFFFF',
            bold = True,
            items = [
                ChapterItem(name = 'Kapazität', unit = 'Ah', number_format = '0.0', fn = lambda design, full_cell: full_cell.safe_capacity_c3),
                ChapterItem(name = 'Energieinhalt', unit = 'Wh', number_format = '0', fn = lambda design, full_cell: full_cell.safe_energy_c3),
                ChapterItem(name = 'Energiedichte volumetrisch', unit = 'Wh/l', number_format = '0', fn = lambda design, full_cell: full_cell.safe_energy_density_l_c3),
                ChapterItem(name = 'Energiedichte gravimetrisch', unit = 'Wh/kg', number_format = '0', fn = lambda design, full_cell: full_cell.safe_energy_density_kg_c3)
            ]
        ),
        Chapter(
            header = 'Fahrzeug',
            fill_color = '3A3838',
            font_color = 'FFFFFF',
            items = [
                ChapterItem(name = 'Zellen parallel', unit = '', number_format = '0', fn = lambda design, full_cell: design.parallel_cells_count),
                ChapterItem(name = 'Zellen seriell', unit = '', number_format = '0', fn = lambda design, full_cell: design.serial_cells_count),
                ChapterItem(name = 'Zellen gesamt', unit = '', number_format = '0', bold = True, fn = lambda design, full_cell: design.parallel_cells_count * design.serial_cells_count),
                ChapterItem(name = 'Nominelle Spannung Pack', unit = 'V', number_format = '0', fn = lambda design, full_cell: full_cell.u_nom * design.serial_cells_count),
                ChapterItem(name = 'Pack-Kapazität', unit = 'Ah', number_format = '0', fn = lambda design, full_cell: full_cell.capacity * (design.parallel_cells_count * design.serial_cells_count)),
                ChapterItem(name = 'Pack-Energie', unit = 'kWh', number_format = '0', bold = True, fn = lambda design, full_cell: (full_cell.energy * (design.parallel_cells_count * design.serial_cells_count)) / 1000),
                ChapterItem(name = 'Gewicht Pack', unit = 'kg', number_format = '0', bold = True, fn = lambda design, full_cell: (full_cell.weight * (design.parallel_cells_count * design.serial_cells_count)) / 1000),
                ChapterItem(name = 'Sicherheitsauslegung Pack-Kapazität', unit = 'Ah', number_format = '0', fn = lambda design, full_cell: full_cell.safe_capacity * (design.parallel_cells_count * design.serial_cells_count)),
                ChapterItem(name = 'Sicherheitsauslegung Pack-Energie', unit = 'kWh', bold = True, number_format = '0', fn = lambda design, full_cell: (full_cell.safe_energy * (design.parallel_cells_count * design.serial_cells_count)) / 1000)
            ]
        ),
        Chapter(
            header = 'Kosten',
            number_format = '_-* #,##0.00 €_-;-* #,##0.00 €_-;_-* "-"?? €_-;_-@_-',
            fill_color = '00B0F0',
            font_color = 'FFFFFF',
            bold = True,
            items = [
                ChapterItem(name = 'Business Case:', unit = '', number_format = '@', fn = lambda design, full_cell: full_cell.business_case),
                ChapterItem(name = 'Ausschuss', unit = '%', number_format = '0.0%', fn = lambda design, full_cell: full_cell.scrap),
                ChapterItem(name = 'Materialpreis pro kWh (C/10)', unit = '€/kWh', fn = lambda design, full_cell: full_cell.price_kwh_c10),
                ChapterItem(name = 'Sicherheitsauslegung Materialpreis pro kWh (C/10)', unit = '€/kWh', fn = lambda design, full_cell: full_cell.safe_price_kwh_c10),
                ChapterItem(name = 'Materialpreis pro kWh (C/3)', unit = '€/kWh', fn = lambda design, full_cell: full_cell.price_kwh_c3),
                ChapterItem(name = 'Sicherheitsauslegung Materialpreis pro kWh (C/3)', unit = '€/kWh', fn = lambda design, full_cell: full_cell.safe_price_kwh_c3),
                ChapterItem(name = 'Materialpreis pro Stück', unit = '€/kWh', fn = lambda design, full_cell: full_cell.price),
                ChapterItem(name = 'Sicherheitsauslegung Materialpreis pro Stück', unit = '€/kWh', fn = lambda design, full_cell: full_cell.safe_price)
            ]
        ),
        Chapter(
            header = 'Kostenaufstellung pro kg',
            number_format = '_-* #,##0.00 €_-;-* #,##0.00 €_-;_-* "-"?? €_-;_-@_-',
            items = [
                ChapterItem(name = 'Kathode', unit = '€/kg', fn = lambda design, full_cell: full_cell.cathode_price_kg),
                ChapterItem(name = 'Aktivmaterial 1', unit = '€/kg', fn = lambda design, full_cell: full_cell.get_price(full_cell.cell_layer.cathode.active_material.materials[0].material)),
                ChapterItem(name = 'Aktivmaterial 2', unit = '€/kg', fn = lambda design, full_cell: full_cell.get_price(full_cell.cell_layer.cathode.active_material.materials[1].material) if len(full_cell.cell_layer.cathode.active_material.materials) > 1 else None),
                ChapterItem(name = 'Binder', unit = '€/kg', fn = lambda design, full_cell: full_cell.get_price(full_cell.cell_layer.cathode.binder_materials[0].material if len(full_cell.cell_layer.cathode.binder_materials) == 1 else None)),
                ChapterItem(name = 'Leitruss', unit = '€/kg', fn = lambda design, full_cell: full_cell.get_price(next((m.material for m in full_cell.cell_layer.cathode.conductive_additive_materials if m.material.id == 'leitruss_c65'), None))),
                ChapterItem(name = 'CNT', unit = '€/kg', fn = lambda design, full_cell: full_cell.get_price(next((m.material for m in full_cell.cell_layer.cathode.conductive_additive_materials if m.material.id == 'cnt'), None))),
                ChapterItem(name = 'Anode', unit = '€/kg', fn = lambda design, full_cell: full_cell.anode_price_kg),
                ChapterItem(name = 'Aktivmaterial 1', unit = '€/kg', fn = lambda design, full_cell: full_cell.get_price(full_cell.cell_layer.anode.active_material.materials[0].material)),
                ChapterItem(name = 'Aktivmaterial 2', unit = '€/kg', fn = lambda design, full_cell: full_cell.get_price(full_cell.cell_layer.anode.active_material.materials[1].material) if len(full_cell.cell_layer.anode.active_material.materials) > 1 else None),
                ChapterItem(name = 'Lithium aus Prälithiierung', unit = '€/kg', fn = lambda design, full_cell: full_cell.get_price(full_cell.cell_layer.anode.lithium_material)),
                ChapterItem(name = 'Binder', unit = '€/kg', fn = lambda design, full_cell: full_cell.get_price(full_cell.cell_layer.anode.binder_materials[0].material if len(full_cell.cell_layer.anode.binder_materials) == 1 else None)),
                ChapterItem(name = 'Leitruss', unit = '€/kg', fn = lambda design, full_cell: full_cell.get_price(next((m.material for m in full_cell.cell_layer.anode.conductive_additive_materials if m.material.id == 'leitruss_c65'), None))),
                ChapterItem(name = 'CNT', unit = '€/kg', fn = lambda design, full_cell: full_cell.get_price(next((m.material for m in full_cell.cell_layer.anode.conductive_additive_materials if m.material.id == 'cnt'), None))),
                ChapterItem(name = 'Sonstiges', unit = '€/kg', fn = lambda design, full_cell: ' ' ),
                ChapterItem(name = 'Separator', unit = '€/kg', fn = lambda design, full_cell: full_cell.get_price(full_cell.cell_layer.separator.material)),
                ChapterItem(name = 'Aluminium Stromableiter', unit = '€/kg', fn = lambda design, full_cell: full_cell.get_price(full_cell.cell_layer.al_cc.material)),
                ChapterItem(name = 'Kupfer Stromableiter', unit = '€/kg', fn = lambda design, full_cell: full_cell.get_price(full_cell.cell_layer.cu_cc.material)),
                ChapterItem(name = 'Elektrolyt', unit = '€/kg', fn = lambda design, full_cell: full_cell.get_price(full_cell.electrolyte)),
                ChapterItem(name = 'Kathoden Tab', unit = '€/kg', fn = lambda design, full_cell: full_cell.get_price(full_cell.cell_format.cathode_tab.material)),
                ChapterItem(name = 'Anoden Tab', unit = '€/kg', fn = lambda design, full_cell: full_cell.get_price(full_cell.cell_format.anode_tab.material)),
                ChapterItem(name = 'Pouchfolie/Gehäuse', unit = '€/kg', fn = lambda design, full_cell: full_cell.get_price(full_cell.cell_format.housing)),
                # ChapterItem(name = 'Kosten Ausschuss', unit = '€/kg', fn = lambda design, full_cell: full_cell.bill_of_materials.loc['Ausschuss', 'price_kg']),
                ChapterItem(name = 'Kosten gesamte Zelle', unit = '€/kg', fn = lambda design, full_cell: full_cell.price_kg)
            ]
        ),
        Chapter(
            header = 'Kostenaufstellung pro kWh (C/10)',
            number_format = '_-* #,##0.00 €_-;-* #,##0.00 €_-;_-* "-"?? €_-;_-@_-',
            items = [
                ChapterItem(name = 'Kathode', unit = '€/kWh', fn = lambda design, full_cell: full_cell.cathode_price / full_cell.energy_c10 * 1e3),
                ChapterItem(name = 'Aktivmaterial 1', unit = '€/kWh', fn = lambda design, full_cell: full_cell.get_price(full_cell.cell_layer.cathode.active_material.materials[0].material) * full_cell.cell_layer.cathode.active_material_weight * full_cell.cell_layer.cathode.active_material.materials[0].weight * full_cell.cathode_weight / full_cell.energy_c10),
                ChapterItem(name = 'Aktivmaterial 2', unit = '€/kWh', fn = lambda design, full_cell: full_cell.get_price(full_cell.cell_layer.cathode.active_material.materials[1].material) * full_cell.cell_layer.cathode.active_material_weight * full_cell.cell_layer.cathode.active_material.materials[1].weight * full_cell.cathode_weight / full_cell.energy_c10 if len(full_cell.cell_layer.cathode.active_material.materials) > 1 else None),
                ChapterItem(name = 'Binder', unit = '€/kWh', fn = lambda design, full_cell: full_cell.get_price(full_cell.cell_layer.cathode.binder_materials[0].material if len(full_cell.cell_layer.cathode.binder_materials) == 1 else None) * (full_cell.cell_layer.cathode.binder_materials[0].weight if len(full_cell.cell_layer.cathode.binder_materials) > 0 else 0) * full_cell.cathode_weight / full_cell.energy_c10),
                ChapterItem(name = 'Leitruss', unit = '€/kWh', fn = lambda design, full_cell: full_cell.get_price(next((m.material for m in full_cell.cell_layer.cathode.conductive_additive_materials if m.material.id == 'leitruss_c65'), None)) * next((m.weight for m in full_cell.cell_layer.cathode.conductive_additive_materials if m.material.id == 'leitruss_c65'), 0) * full_cell.cathode_weight / full_cell.energy_c10),
                ChapterItem(name = 'CNT', unit = '€/kWh', fn = lambda design, full_cell: full_cell.get_price(next((m.material for m in full_cell.cell_layer.cathode.conductive_additive_materials if m.material.id == 'cnt'), None)) * next((m.weight for m in full_cell.cell_layer.cathode.conductive_additive_materials if m.material.id == 'cnt'), 0) * full_cell.cathode_weight / full_cell.energy_c10),
                ChapterItem(name = 'Anode', unit = '€/kWh', fn = lambda design, full_cell: full_cell.anode_price / full_cell.energy_c10 * 1e3),
                ChapterItem(name = 'Aktivmaterial 1', unit = '€/kWh', fn = lambda design, full_cell: full_cell.get_price(full_cell.cell_layer.anode.active_material.materials[0].material) * full_cell.cell_layer.anode.active_material_weight * full_cell.cell_layer.anode.active_material.materials[0].weight * full_cell.anode_weight / full_cell.energy_c10),
                ChapterItem(name = 'Aktivmaterial 2', unit = '€/kWh', fn = lambda design, full_cell: full_cell.get_price(full_cell.cell_layer.anode.active_material.materials[1].material) * full_cell.cell_layer.anode.active_material_weight * full_cell.cell_layer.anode.active_material.materials[1].weight * full_cell.anode_weight / full_cell.energy_c10 if len(full_cell.cell_layer.anode.active_material.materials) > 1 else None),
                ChapterItem(name = 'Lithium aus Prälithiierung', unit = '€/kWh', fn = lambda design, full_cell: full_cell.get_price(full_cell.cell_layer.anode.lithium_material) * full_cell.cell_layer.anode.lithium_weight * full_cell.anode_weight / full_cell.energy_c10),
                ChapterItem(name = 'Binder', unit = '€/kWh', fn = lambda design, full_cell: full_cell.get_price(full_cell.cell_layer.anode.binder_materials[0].material if len(full_cell.cell_layer.anode.binder_materials) == 1 else None) * (full_cell.cell_layer.anode.binder_materials[0].weight if len(full_cell.cell_layer.anode.binder_materials) > 0 else 0) * full_cell.anode_weight / full_cell.energy_c10),
                ChapterItem(name = 'Leitruss', unit = '€/kWh', fn = lambda design, full_cell: full_cell.get_price(next((m.material for m in full_cell.cell_layer.anode.conductive_additive_materials if m.material.id == 'leitruss_c65'), None)) * next((m.weight for m in full_cell.cell_layer.anode.conductive_additive_materials if m.material.id == 'leitruss_c65'), 0) * full_cell.anode_weight / full_cell.energy_c10),
                ChapterItem(name = 'CNT', unit = '€/kWh', fn = lambda design, full_cell: full_cell.get_price(next((m.material for m in full_cell.cell_layer.anode.conductive_additive_materials if m.material.id == 'cnt'), None)) * next((m.weight for m in full_cell.cell_layer.anode.conductive_additive_materials if m.material.id == 'cnt'), 0) * full_cell.anode_weight / full_cell.energy_c10),
                ChapterItem(name = 'Sonstiges', unit = '€/kWh', fn = lambda design, full_cell: ' ' ),
                ChapterItem(name = 'Separator', unit = '€/kWh', fn = lambda design, full_cell: full_cell.get_price(full_cell.cell_layer.separator.material) * full_cell.cell_layer.separator.weight * full_cell.cell_format.no_separator_layers / full_cell.energy_c10),
                ChapterItem(name = 'Aluminium Stromableiter', unit = '€/kWh', fn = lambda design, full_cell: full_cell.get_price(full_cell.cell_layer.al_cc.material) * full_cell.cell_layer.al_cc.weight * full_cell.cell_format.no_cathode_layers / full_cell.energy_c10),
                ChapterItem(name = 'Kupfer Stromableiter', unit = '€/kWh', fn = lambda design, full_cell: full_cell.get_price(full_cell.cell_layer.cu_cc.material) * full_cell.cell_layer.cu_cc.weight * full_cell.cell_format.no_anode_layers / full_cell.energy_c10),
                ChapterItem(name = 'Elektrolyt', unit = '€/kWh', fn = lambda design, full_cell: full_cell.get_price(full_cell.electrolyte) * full_cell.electrolyte.weight / full_cell.energy_c10),
                ChapterItem(name = 'Kathoden Tab', unit = '€/kWh', fn = lambda design, full_cell: full_cell.get_price(full_cell.cell_format.cathode_tab.material) * full_cell.cell_format.cathode_tab.weight / full_cell.energy_c10),
                ChapterItem(name = 'Anoden Tab', unit = '€/kWh', fn = lambda design, full_cell: full_cell.get_price(full_cell.cell_format.anode_tab.material) * full_cell.cell_format.anode_tab.weight / full_cell.energy_c10),
                ChapterItem(name = 'Pouchfolie/Gehäuse', unit = '€/kWh', fn = lambda design, full_cell: full_cell.get_price(full_cell.cell_format.housing) * full_cell.cell_format.housing_weight / full_cell.energy_c10),
                # ChapterItem(name = 'Kosten Ausschuss', unit = '€/kWh', fn = lambda design, full_cell: full_cell.bill_of_materials.loc['Ausschuss', 'price_kg']),
                ChapterItem(name = 'Kosten gesamte Zelle', unit = '€/kWh', fn = lambda design, full_cell: full_cell.price / full_cell.energy_c10 * 1e3)
            ]
        ),
        Chapter(
            header = 'Kostenaufstellung pro Zelle',
            number_format = '_-* #,##0.00 €_-;-* #,##0.00 €_-;_-* "-"?? €_-;_-@_-',
            items = [
                ChapterItem(name = 'Kathode', unit = '€/Zelle', fn = lambda design, full_cell: full_cell.cathode_price),
                ChapterItem(name = 'Aktivmaterial 1', unit = '€/Zelle', fn = lambda design, full_cell: full_cell.get_price(full_cell.cell_layer.cathode.active_material.materials[0].material) * full_cell.cell_layer.cathode.active_material_weight * full_cell.cell_layer.cathode.active_material.materials[0].weight * full_cell.cathode_weight / 1000),
                ChapterItem(name = 'Aktivmaterial 2', unit = '€/Zelle', fn = lambda design, full_cell: full_cell.get_price(full_cell.cell_layer.cathode.active_material.materials[1].material) * full_cell.cell_layer.cathode.active_material_weight * full_cell.cell_layer.cathode.active_material.materials[1].weight * full_cell.cathode_weight / 1000 if len(full_cell.cell_layer.cathode.active_material.materials) > 1 else None),
                ChapterItem(name = 'Binder', unit = '€/Zelle', fn = lambda design, full_cell: full_cell.get_price(full_cell.cell_layer.cathode.binder_materials[0].material if len(full_cell.cell_layer.cathode.binder_materials) == 1 else None) * (full_cell.cell_layer.cathode.binder_materials[0].weight if len(full_cell.cell_layer.cathode.binder_materials) == 1 else 0) * full_cell.cathode_weight / 1000),
                ChapterItem(name = 'Leitruss', unit = '€/Zelle', fn = lambda design, full_cell: full_cell.get_price(next((m.material for m in full_cell.cell_layer.cathode.conductive_additive_materials if m.material.id == 'leitruss_c65'), None)) * next((m.weight for m in full_cell.cell_layer.cathode.conductive_additive_materials if m.material.id == 'leitruss_c65'), 0) * full_cell.cathode_weight / 1000),
                ChapterItem(name = 'CNT', unit = '€/Zelle', fn = lambda design, full_cell: full_cell.get_price(next((m.material for m in full_cell.cell_layer.cathode.conductive_additive_materials if m.material.id == 'cnt'), None)) * next((m.weight for m in full_cell.cell_layer.cathode.conductive_additive_materials if m.material.id == 'cnt'), 0) * full_cell.cathode_weight / 1000),
                ChapterItem(name = 'Anode', unit = '€/Zelle', fn = lambda design, full_cell: full_cell.anode_price),
                ChapterItem(name = 'Aktivmaterial 1', unit = '€/Zelle', fn = lambda design, full_cell: full_cell.get_price(full_cell.cell_layer.anode.active_material.materials[0].material) * full_cell.cell_layer.anode.active_material_weight * full_cell.cell_layer.anode.active_material.materials[0].weight * full_cell.anode_weight / 1000),
                ChapterItem(name = 'Aktivmaterial 2', unit = '€/Zelle', fn = lambda design, full_cell: full_cell.get_price(full_cell.cell_layer.anode.active_material.materials[1].material) * full_cell.cell_layer.anode.active_material_weight * full_cell.cell_layer.anode.active_material.materials[1].weight * full_cell.anode_weight / 1000 if len(full_cell.cell_layer.anode.active_material.materials) > 1 else None),
                ChapterItem(name = 'Lithium aus Prälithiierung', unit = '€/Zelle', fn = lambda design, full_cell: full_cell.get_price(full_cell.cell_layer.anode.lithium_material) * full_cell.cell_layer.anode.lithium_weight * full_cell.anode_weight / 1000),
                ChapterItem(name = 'Binder', unit = '€/Zelle', fn = lambda design, full_cell: full_cell.get_price(full_cell.cell_layer.anode.binder_materials[0].material if len(full_cell.cell_layer.anode.binder_materials) == 1 else None) * (full_cell.cell_layer.anode.binder_materials[0].weight if len(full_cell.cell_layer.anode.binder_materials) == 1 else 0) * full_cell.anode_weight / 1000),
                ChapterItem(name = 'Leitruss', unit = '€/Zelle', fn = lambda design, full_cell: full_cell.get_price(next((m.material for m in full_cell.cell_layer.anode.conductive_additive_materials if m.material.id == 'leitruss_c65'), None)) * next((m.weight for m in full_cell.cell_layer.anode.conductive_additive_materials if m.material.id == 'leitruss_c65'), 0) * full_cell.anode_weight / 1000),
                ChapterItem(name = 'CNT', unit = '€/Zelle', fn = lambda design, full_cell: full_cell.get_price(next((m.material for m in full_cell.cell_layer.anode.conductive_additive_materials if m.material.id == 'cnt'), None)) * next((m.weight for m in full_cell.cell_layer.anode.conductive_additive_materials if m.material.id == 'cnt'), 0) * full_cell.anode_weight / 1000),
                ChapterItem(name = 'Sonstiges', unit = '€/Zelle', fn = lambda design, full_cell: ' ' ),
                ChapterItem(name = 'Separator', unit = '€/Zelle', fn = lambda design, full_cell: full_cell.get_price(full_cell.cell_layer.separator.material) * full_cell.cell_layer.separator.weight * full_cell.cell_format.no_separator_layers / 1000),
                ChapterItem(name = 'Aluminium Stromableiter', unit = '€/Zelle', fn = lambda design, full_cell: full_cell.get_price(full_cell.cell_layer.al_cc.material) * full_cell.cell_layer.al_cc.weight * full_cell.cell_format.no_cathode_layers / 1000),
                ChapterItem(name = 'Kupfer Stromableiter', unit = '€/Zelle', fn = lambda design, full_cell: full_cell.get_price(full_cell.cell_layer.cu_cc.material) * full_cell.cell_layer.cu_cc.weight * full_cell.cell_format.no_anode_layers / 1000),
                ChapterItem(name = 'Elektrolyt', unit = '€/Zelle', fn = lambda design, full_cell: full_cell.get_price(full_cell.electrolyte) * full_cell.electrolyte.weight / 1000),
                ChapterItem(name = 'Kathoden Tab', unit = '€/Zelle', fn = lambda design, full_cell: full_cell.get_price(full_cell.cell_format.cathode_tab.material) * full_cell.cell_format.cathode_tab.weight / 1000),
                ChapterItem(name = 'Anoden Tab', unit = '€/Zelle', fn = lambda design, full_cell: full_cell.get_price(full_cell.cell_format.anode_tab.material) * full_cell.cell_format.anode_tab.weight / 1000),
                ChapterItem(name = 'Pouchfolie/Gehäuse', unit = '€/Zelle', fn = lambda design, full_cell: full_cell.get_price(full_cell.cell_format.housing) * full_cell.cell_format.housing_weight / 1000),
                # ChapterItem(name = 'Kosten Ausschuss', unit = '€/Zelle', fn = lambda design, full_cell: full_cell.bill_of_materials.loc['Ausschuss', 'price_kg']),
                ChapterItem(name = 'Kosten gesamte Zelle', unit = '€/Zelle', fn = lambda design, full_cell: full_cell.price)
            ]
        )
    ]
      
    return chapters

def generate_excel(designs: list[Union[CellDesignModel, CellDesignModelWithId]]) -> Iterable[bytes]:

    chapters = create_chapters()

    column_count = len(designs) + 2

    workbook = load_workbook(filename = os.path.join(os.getcwd(), 'data', 'xlsx_template.xlsx'))
    ws = workbook.active
    ws.title = 'Design'

    # column dimensions
    ws.column_dimensions[get_column_letter(1)].width = 55
    ws.column_dimensions[get_column_letter(2)].width = 15
    for col in range(3, column_count + 1):
        ws.column_dimensions[get_column_letter(col)].width = 30

    # logo
    ws.merge_cells('A1:A2')
    logo_path = os.path.join(os.getcwd(), 'data', 'img', 'logo_excel.png')
    img = openpyxl.drawing.image.Image(logo_path)
    ws.add_image(img, 'A1')

    ws.merge_cells(f'B1:{get_column_letter(column_count)}2')
    ws['B1'] = 'Erste Hochrechnungen auf Basis von Halbzellmessungen'
    ws['B1'].alignment = Alignment(horizontal='center', vertical='center')

    ws.merge_cells('A3:A9')
    ws['A3'] = 'CFG-Zellkalkulator Ergebnisse'
    ws['A3'].font = Font(size = 22, color = 'FFFFFF')
    ws['A3'].fill = PatternFill(start_color = '00B0F0', end_color = '00B0F0', fill_type = 'solid')
    ws['A3'].alignment = Alignment(horizontal='center', vertical='center')

    ws['B3'] = 'Export Version'
    ws['B3'].font = Font(color = 'FFFFFF')
    ws['B3'].fill = PatternFill(start_color = '00B0F0', end_color = '00B0F0', fill_type = 'solid')

    ws['B4'] = 'Design Version'
    ws['B4'].font = Font(color = 'FFFFFF')
    ws['B4'].fill = PatternFill(start_color = '00B0F0', end_color = '00B0F0', fill_type = 'solid')

    ws['B5'] = 'Datum'
    ws['B5'].font = Font(color = 'FFFFFF')
    ws['B5'].fill = PatternFill(start_color = '00B0F0', end_color = '00B0F0', fill_type = 'solid')

    ws['B6'] = 'Erstellt von'
    ws['B6'].font = Font(color = 'FFFFFF')
    ws['B6'].fill = PatternFill(start_color = '00B0F0', end_color = '00B0F0', fill_type = 'solid')

    ws['B7'] = 'Design ID'
    ws['B7'].font = Font(color = 'FFFFFF')
    ws['B7'].fill = PatternFill(start_color = '00B0F0', end_color = '00B0F0', fill_type = 'solid')

    ws['B8'] = 'Freigabestatus'
    ws['B8'].font = Font(color = 'FFFFFF')
    ws['B8'].fill = PatternFill(start_color = '00B0F0', end_color = '00B0F0', fill_type = 'solid')

    ws['B9'] = 'Part.Nummer'
    ws['B9'].font = Font(color = 'FFFFFF')
    ws['B9'].fill = PatternFill(start_color = '00B0F0', end_color = '00B0F0', fill_type = 'solid')

    ws.merge_cells(f'A10:{get_column_letter(column_count)}10')
    ws['A10'] = 'Freigabe'
    ws['A10'].font = Font(b=True)
    
    ws['A11'] = 'Beschreibung'
    ws['A11'].font = Font(color = 'FFFFFF', b = True)
    ws['A11'].fill = PatternFill(start_color = '3C3C3C', end_color = '3C3C3C', fill_type = 'solid')

    ws['B11'] = 'Einheit'
    ws['B11'].font = Font(color = 'FFFFFF', b = True)
    ws['B11'].fill = PatternFill(start_color = '3C3C3C', end_color = '3C3C3C', fill_type = 'solid')

    full_cells = [create_full_cell(design) for design in designs]

    for (design_index, design) in enumerate(designs):
        column_letter = get_column_letter(design_index + 3)

        ws[f'{column_letter}3'] = VERSION
        ws[f'{column_letter}3'].font = Font(color = 'FFFFFF')
        ws[f'{column_letter}3'].fill = PatternFill(start_color = '00B0F0', end_color = '00B0F0', fill_type = 'solid')

        ws[f'{column_letter}4'] = design.version if isinstance(design, CellDesignModelWithId) and design.created_at is not None else ''
        ws[f'{column_letter}4'].font = Font(color = 'FFFFFF')
        ws[f'{column_letter}4'].fill = PatternFill(start_color = '00B0F0', end_color = '00B0F0', fill_type = 'solid')

        ws[f'{column_letter}5'] = design.created_at.strftime('%Y-%m-%d') if isinstance(design, CellDesignModelWithId) and design.created_at is not None else ''
        ws[f'{column_letter}5'].font = Font(color = 'FFFFFF')
        ws[f'{column_letter}5'].fill = PatternFill(start_color = '00B0F0', end_color = '00B0F0', fill_type = 'solid')

        ws[f'{column_letter}6'] = design.created_by.name if isinstance(design, CellDesignModelWithId) and design.created_by is not None else ''
        ws[f'{column_letter}6'].font = Font(color = 'FFFFFF')
        ws[f'{column_letter}6'].fill = PatternFill(start_color = '00B0F0', end_color = '00B0F0', fill_type = 'solid')

        ws[f'{column_letter}7'] = design.design_id if isinstance(design, CellDesignModelWithId) else ''
        ws[f'{column_letter}7'].font = Font(color = 'FFFFFF')
        ws[f'{column_letter}7'].fill = PatternFill(start_color = '00B0F0', end_color = '00B0F0', fill_type = 'solid')

        ws[f'{column_letter}8'] = ('Freigegeben' if design.released else 'Nicht freigegeben') if isinstance(design, CellDesignModelWithId) else ''
        ws[f'{column_letter}8'].font = Font(color = 'FFFFFF')
        ws[f'{column_letter}8'].fill = PatternFill(start_color = '00B0F0', end_color = '00B0F0', fill_type = 'solid')

        ws[f'{column_letter}9'] = design.part_number if isinstance(design, CellDesignModelWithId) else ''
        ws[f'{column_letter}9'].font = Font(color = 'FFFFFF')
        ws[f'{column_letter}9'].fill = PatternFill(start_color = '00B0F0', end_color = '00B0F0', fill_type = 'solid')

        ws[f'{column_letter}11'] = f'Design {design_index + 1}'
        ws[f'{column_letter}11'].font = Font(color = 'FFFFFF', b = True)
        ws[f'{column_letter}11'].fill = PatternFill(start_color = '3C3C3C', end_color = '3C3C3C', fill_type = 'solid')

    def write_chapter(chapter: Union[Chapter, ChapterItem], row: int, level: int, number_format: Optional[str], fill_color: Optional[str], font_color: Optional[str], bold: bool) -> int:
        if isinstance(chapter, Chapter):
            # determine styles
            number_format = chapter.number_format if chapter.number_format is not None else number_format
            fill_color = chapter.fill_color if chapter.fill_color is not None else fill_color
            font_color = chapter.font_color if chapter.font_color is not None else font_color
            bold = chapter.bold if chapter.bold is not None else bold

            # write header
            ws[f'A{row}'] = chapter.header

            # format cells
            for col in range(1, column_count + 1):
                header_cell = ws[f'{get_column_letter(col)}{row}']
                if fill_color is not None:
                    header_cell.fill = PatternFill(start_color = fill_color, end_color = fill_color, fill_type = 'solid')

                if level == 0:
                    header_cell.border = Border(top = Side(border_style = 'thick', color = '000000'))
                    header_cell.font = Font(b = True, size = 14, color = font_color)
                    header_cell.alignment = Alignment(horizontal = 'center')
                elif level > 0:
                    header_cell.font = Font(b = True, size = 12, color = font_color)
            row += 1
            for item in chapter.items:
                row = write_chapter(item, row, level + 1, number_format, fill_color, font_color, bold)
        elif isinstance(chapter, ChapterItem):
            # determine styles
            number_format = chapter.number_format if chapter.number_format is not None else number_format
            fill_color = chapter.fill_color if chapter.fill_color is not None else fill_color
            font_color = chapter.font_color if chapter.font_color is not None else font_color
            bold = chapter.bold if chapter.bold is not None else bold

            # write keys
            ws[f'A{row}'] = chapter.name
            ws[f'B{row}'] = chapter.unit

            # write values
            for (design_index, (design, full_cell)) in enumerate(zip(designs, full_cells)):
                column_letter = get_column_letter(design_index + 3)
                try:
                    value = chapter.fn(design, full_cell)
                except:
                    value = None

                if value is not None:
                    if isinstance(value, numbers.Number):
                        if math.isnan(value):
                            value = None
                    else:
                        value = str(value)

                if value is not None:
                    ws[f'{column_letter}{row}'] = value
            
            # format cells
            for col in range(1, column_count + 1):
                item_cell = ws[f'{get_column_letter(col)}{row}']
                if number_format is not None and col > 2:
                    item_cell.number_format = number_format
                if fill_color is not None:
                    item_cell.fill = PatternFill(start_color = fill_color, end_color = fill_color, fill_type = 'solid')
                item_cell.font = Font(b = bold, color = font_color)

            row += 1
        return row

    row = 12
    for chapter in chapters:
        row = write_chapter(chapter, row, 0, None, None, None, False)

    buffer = io.BytesIO()
    file = zipfile.ZipFile(buffer, mode = 'w', compression = zipfile.ZIP_DEFLATED)
    ExcelWriter(workbook, file).save()
    buffer.flush()
    buffer.seek(0)

    # write chunks to the client
    b = buffer.read(1024)
    while b is not None and len(b) != 0:
        yield b
        b = buffer.read(1024)
