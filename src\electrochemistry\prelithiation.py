from dataclasses import dataclass
from enum import Enum
from typing import Optional
from electrochemistry.material import Material, lithium, li_cl

class PrelithiationProcess(Enum):
    NANOSCALE = 0
    GROUP_14 = 1

@dataclass
class Prelithiation:

    capacity: Optional[float]
    process: PrelithiationProcess
    lithium: Material

    def __init__(self, capacity: Optional[float], process: PrelithiationProcess):
        self.capacity = capacity
        self.process = process
        
        if self.process == PrelithiationProcess.NANOSCALE:
            self.lithium = li_cl
        elif self.process == PrelithiationProcess.GROUP_14:
            self.lithium = lithium

    def get_lithium_weight(self) -> float:
        return self.capacity / lithium.theoretical_capacity
