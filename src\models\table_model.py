from dataclasses import dataclass
from typing import Optional, TypedDict, Union

class TableCellEditModel(TypedDict):
    id: str
    min: Optional[float]
    max: Optional[float]

class TableCellModel(TypedDict):
    value: Optional[Union[float, str]]
    unit: Optional[str]
    editable: Optional[TableCellEditModel]
    is_primary: Optional[bool]

class TableRowModel(TypedDict):
    cells: list[TableCellModel]

class TableModel(TypedDict):
    headers: list[str]
    rows: list[TableRowModel]
