import numpy as np
import os
from abc import abstractmethod
from copy import deepcopy
from bson import ObjectId
import itertools
from pymoo.core.algorithm import Algorithm
from pymoo.algorithms.moo.nsga2 import NSGA2
from pymoo.algorithms.soo.nonconvex.pso import PSO
from pymoo.optimize import minimize
from pymoo.core.problem import Problem
from typing import Optional, Type, List, Any, Union

import torch
from botorch.models import SingleTaskGP
from botorch.fit import fit_gpytorch_mll
from botorch.optim import optimize_acqf
from botorch.acquisition import LogExpectedImprovement
from gpytorch.mlls import ExactMarginalLogLikelihood

from electrochemistry.factory import create_full_cell
from electrochemistry.material_lookup import MaterialLookup
from electrochemistry.full_cell import FullCell
from models.cell_design_model import CellDesignModel
from models.optimization_result_model import OptimizationResultModel, OptimizationHistory
from models.optimization_request_model import OptimizationSettingsRequestModel
from models.optimization_settings_model import StopCriterionType, AlgorithmType
from utils.mapped_attributes import set_mapped_attribute, get_mapped_attribute

full_cell_field_paths = {
    'cost': 'price',
    'capacity':'safe_capacity',
    'energy':'safe_energy',
    'energy_density_vol':'safe_energy_density_l',
    'energy_density_grav':'safe_energy_density_kg',
    'weight':'weight',
    "cf3_breathing_with_compression":"cf3_breathing_with_compression"
}

design_field_paths = {
    'anode_weight_0': 'anode_materials[0].weight_percent',
    'anode_weight_1': 'anode_materials[1].weight_percent',
    'cathode_weight_0': 'cathode_materials[0].weight_percent',
    'cathode_weight_1': 'cathode_materials[1].weight_percent'
}

material_lookup_paths = {
    'group14_scc55': os.path.join(os.getcwd(), 'data', 'optimization', 'SSCLookUpTable.csv'),
    'basf_ncm90_pc_bt98b': os.path.join(os.getcwd(), 'data', 'optimization', 'NMC90_LMFP_LookUpTable.csv'),
}

# Design wrapper to do value application.
# It is assumed that same fields will be applied and which won't affect other fields.
class DesignTemplate:
    name: str
    cell_design: CellDesignModel

    def __init__(self, cell_design: CellDesignModel, name: str = 'default'):
        self.name = name
        self.cell_design = deepcopy(cell_design)
    
    @staticmethod
    def apply_value(cell_design, field, value):
        field_path = design_field_paths.get(field, field)
        set_mapped_attribute(cell_design, field_path, value)

    def apply_params(self, fields: List[str], values: Union[np.ndarray, tuple[float]]):
        for (field, value) in zip(fields, values):
            self.apply_value(self.cell_design, field, value)
        return self.cell_design


class WeightDesignTemplate(DesignTemplate):

    anode_material_lookup: Optional[MaterialLookup]
    cathode_material_lookup: Optional[MaterialLookup]

    def __init__(self, cell_design: CellDesignModel):
        super().__init__(cell_design, 'weight')

        self.anode_material_lookup = None
        self.cathode_material_lookup = None

        anode_material = cell_design.anode_materials[0].material_id if cell_design.anode_materials else None
        if anode_material in material_lookup_paths:
            self.anode_material_lookup = MaterialLookup(anode_material, material_lookup_paths[anode_material])

        cathode_material = cell_design.cathode_materials[0].material_id if cell_design.cathode_materials else None
        if cathode_material in material_lookup_paths:
            self.cathode_material_lookup = MaterialLookup(cathode_material, material_lookup_paths[cathode_material])

    def apply_params(self, fields: List[str], values: np.ndarray):
        for (field, value) in zip(fields, values):
            # Weight only works up to 2 decimal places for lookup table
            if field == 'anode_weight_0':
                value = round(value, 2)
                adjacent_field = 'anode_weight_1'
                adjacent_value = 100 - value
                self.apply_value(self.cell_design, adjacent_field, adjacent_value)

                if self.anode_material_lookup:
                    lookup_field = 'anode_calander_density'
                    lookup_value = self.anode_material_lookup.get_value(value)
                    self.apply_value(self.cell_design, lookup_field, lookup_value)

            if field == 'cathode_weight_0':
                value = round(value, 2)
                adjacent_field = 'cathode_weight_1'
                adjacent_value = 100 - value
                self.apply_value(self.cell_design, adjacent_field, adjacent_value)

                if self.cathode_material_lookup:
                    lookup_field = 'cathode_calander_density'
                    lookup_value = self.cathode_material_lookup.get_value(value)
                    self.apply_value(self.cell_design, lookup_field, lookup_value)

            self.apply_value(self.cell_design, field, value)

        return self.cell_design



class Optimization():

    _running_optimizations = {}
    
    def __init__(self, original_design: CellDesignModel, optimization_setting: OptimizationSettingsRequestModel, optimization_id: ObjectId):
        Optimization._running_optimizations[optimization_id] = self
        self._design = deepcopy(original_design)
        self._optimization_setting = optimization_setting
        self._optimized_values = []
        self._history = []
        self._xl = [] # mins
        self._xu = [] # maxs
        self._variables, variable_mins, variable_maxs= map(list, zip(*[(m.id, m.min_value, m.max_value) for m in self._optimization_setting.variables]))
        self._xl.extend(variable_mins)
        self._xu.extend(variable_maxs)
        self._iteration_counter = 0

        self._original_full_cell = create_full_cell(original_design)
        self._original_cell_value = getattr(self._original_full_cell, full_cell_field_paths.get(optimization_setting.objective, optimization_setting.objective))
      
        for v in self._optimization_setting.variables:
            v.original = get_mapped_attribute(original_design, design_field_paths.get(v.id, v.id))

        if 'anode_weight_0' in self._variables or 'cathode_weight_0' in self._variables:
            self._design_template = WeightDesignTemplate(self._design)
        else:
            self._design_template = DesignTemplate(self._design)

    def progress(self):
        if self.getPointCount() != 0:
            return round((self._iteration_counter/self.getPointCount())*100, 1)
        else:
            return 0

    @abstractmethod
    def getPointCount(self):
        pass

    @abstractmethod
    def optimize(self) -> OptimizationResultModel:
        pass

class BruteOptimization(Optimization):

    def __init__(self, original_design: CellDesignModel, optimization_setting: OptimizationSettingsRequestModel, optimization_id: ObjectId):
        super().__init__(original_design,optimization_setting, optimization_id)
        self._steps = [m.step_size for m in self._optimization_setting.variables]
    
    def _generate_range_with_step(self,min_value, max_value, step_size) -> np.ndarray:
        values = np.arange(min_value, max_value, step_size)
        if values[-1] != max_value:
            values = np.append(values, max_value)
        return values
            
    def _generate_range_iterations_with_step(self):
        ranges = [
            self._generate_range_with_step(min, max, step)  # Include max_val if not reached by step
            for min, max, step in zip(self._xl, self._xu, self._steps)
        ]
        return itertools.product(*ranges)

    def _evaluate_optimum(self, optimization_target: str, cell_value:float, optimum:float) -> bool:
        if optimization_target == "min":
            return cell_value < optimum
        else:
            return cell_value >= optimum

    def getPointCount(self):
        return np.prod([
            len(self._generate_range_with_step(min, max, step))
            for min, max, step in zip(self._xl, self._xu, self._steps)
        ])

    def optimize(self):
        objective = self._optimization_setting.objective
        optimum = None
        for params_values in self._generate_range_iterations_with_step():
            cell_design = self._design_template.apply_params(self._variables, params_values)
            full_cell = create_full_cell(cell_design)
            cell_value = getattr(full_cell, full_cell_field_paths.get(objective, objective))
            if optimum is None or self._evaluate_optimum(self._optimization_setting.optimization_target, cell_value, optimum):
                optimum = cell_value
                self._optimized_values = list(params_values)
            self._history.append((cell_value, params_values))
            self._iteration_counter +=1

        return OptimizationResultModel(
            variables=self._variables,
            history=self._history,
            optimized_values=self._optimized_values,
            optimization_result_value=optimum,
            original_value=self._original_cell_value
        )
    

class ProblemWrapper(Problem):
    design_template: DesignTemplate
    metrics: list[str]  # for order
    objective: str

    def __init__(self, design_template: DesignTemplate, metrics: list[str], objective: str, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.design_template = design_template
        self.metrics = metrics
        self.objective = objective

    # vars_gens is the population of generated variable values.
    def _evaluate(self, vars_gens, out, *args, **kwargs):
        res = []
        for vars in vars_gens:
            cell_design = self.design_template.apply_params(self.metrics, vars)
            full_cell = create_full_cell(cell_design)
            value = getattr(full_cell, full_cell_field_paths.get(self.objective, self.objective))

            res.append(value)
        out['F'] = np.array(res)

class NSGA2Optimization(Optimization):   

    def __init__(self, original_design: CellDesignModel, optimization_setting: OptimizationSettingsRequestModel, optimization_id: ObjectId):
        super().__init__(original_design,optimization_setting, optimization_id)
        self._n_var = len(self._optimization_setting.variables)

    @staticmethod
    def eval_termination(type: StopCriterionType, value: any):
        if type == 'time':
            value *= 60
        return (type, value)

    def getPointCount(self):
        return 0

    def optimize(self):
        algorithm = NSGA2(pop_size=self._optimization_setting.population_size)
        termination = NSGA2Optimization.eval_termination(self._optimization_setting.stop_criterion_type, self._optimization_setting.stop_criterion_value)
        problem = ProblemWrapper(
            self._design_template,
            self._variables,
            self._optimization_setting.objective,
            n_var=self._n_var,
            n_obj=1,
            xl=self._xl,
            xu=self._xu)

        results = minimize(
            problem=problem,
            algorithm=algorithm,
            termination=termination,
            save_history=True)

        self._optimized_values = results.X
        # Only support single objective optimization.
        self._history = [(e.opt[0].F[0], list(e.opt[0].X)) for e in results.history]
        print('Optimization: Execution time', results.exec_time)

        return OptimizationResultModel(
            variables=self._variables,
            history=self._history,
            optimized_values=self._optimized_values,
            optimization_result_value=results.Y,
            original_value=self._original_cell_value
        )

class BayesianOptimization(Optimization):
    class BayesianObjectiveFunction:
        def __init__(self, bayesian_optimization):
            self._bayesian_optimization = bayesian_optimization

        def __call__(self, value):
            optimization_settings = self._bayesian_optimization._optimization_setting
            variables = [m.id for m in optimization_settings.variables]
            res = []
            for x in value:
                cell_design = self._bayesian_optimization._design_template.apply_params(variables, x.numpy())
                full_cell = create_full_cell(cell_design)
                cell_value = getattr(full_cell, full_cell_field_paths.get(optimization_settings.objective, optimization_settings.objective))
                res.append( torch.DoubleTensor([cell_value, ]))
                self._bayesian_optimization._history.append((cell_value, list(x.numpy())))
                self._bayesian_optimization._iteration_counter +=1
            if self._bayesian_optimization._optimization_setting.optimization_target == "min":
                return torch.DoubleTensor(res) * -1
            return torch.DoubleTensor(res)

    
    def __init__(self , original_design: CellDesignModel, optimization_setting: OptimizationSettingsRequestModel, optimization_id: ObjectId):
        super().__init__(original_design, optimization_setting, optimization_id)
        self.objective_function = BayesianOptimization.BayesianObjectiveFunction(self)
        self.bounds = torch.tensor([self._xl, self._xu])
        self._num_iterations = self._optimization_setting.num_iterations
        self._num_initial_points = self._optimization_setting.num_initial_points
        self.dim = self.bounds.shape[1]
        self.train_X = torch.rand(self._num_initial_points, self.dim, dtype=torch.float64) * (self.bounds[1] - self.bounds[0]) + self.bounds[0]
        self.train_Y = self.objective_function(self.train_X).unsqueeze(-1)

    def _fit_model(self):
        gp = SingleTaskGP(self.train_X, self.train_Y)
        mll = ExactMarginalLogLikelihood(gp.likelihood, gp)
        fit_gpytorch_mll(mll)
        return gp

    def _optimize_acquisition(self, model):
        ei = LogExpectedImprovement(model, best_f=self.train_Y.max())

        new_X, _ = optimize_acqf(
            ei,
            bounds=self.bounds,
            q=1,
            num_restarts=5,
            raw_samples=20,
        )
        return new_X.to(torch.double)

    def getPointCount(self):
        return self._num_initial_points+self._num_iterations

    def optimize(self):
        for _ in range(self._num_iterations):
            model = self._fit_model()
            new_X = self._optimize_acquisition(model)
            new_Y = self.objective_function(new_X).unsqueeze(-1)

            self.train_X = torch.cat([self.train_X, new_X])
            self.train_Y = torch.cat([self.train_Y, new_Y])

        best_idx = self.train_Y.argmax()
        best_X = self.train_X[best_idx]
        if self._optimization_setting.optimization_target == "min":
            best_Y = self.train_Y[best_idx] * -1
        else:
            best_Y = self.train_Y[best_idx]

        return OptimizationResultModel(
            variables=self._variables,
            history=self._history,
            optimized_values=list(best_X.numpy()),
            optimization_result_value=best_Y.item(),
            original_value=self._original_cell_value
        )


def optimize(original_design: CellDesignModel, optimization: OptimizationSettingsRequestModel, optimization_id: ObjectId) -> OptimizationResultModel:
    opt = BruteOptimization(original_design, optimization, optimization_id)
    if optimization.algorithm == "nsga2":
        opt = NSGA2Optimization(original_design, optimization, optimization_id)
    if optimization.algorithm == "bayesian":
        opt = BayesianOptimization(original_design, optimization, optimization_id)

    optimization_result = opt.optimize()

    del Optimization._running_optimizations[optimization_id]
    return optimization_result