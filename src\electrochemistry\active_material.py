from dataclasses import dataclass
from enum import Enum
from typing import Optional
from pandas import DataFrame
from electrochemistry.formation_table import FormationTable
from electrochemistry.material import Material, MaterialType, MaterialWeight

class ActiveMaterialType(Enum):
    CATHODE = 0
    ANODE = 1

class ActiveMaterial(Material):

    active_material_type: ActiveMaterialType
    composition: list[MaterialWeight]
    formations: FormationTable
    formations_history: dict

    def __init__(self, id: str, name: str, density: float, type: MaterialType, active_material_type: ActiveMaterialType, formations: FormationTable, formations_history: dict, composition: Optional[list[MaterialWeight]] = None, active_surface: Optional[float] = None):
        if composition is None:
            composition = [MaterialWeight(self, 1.0)]

        super().__init__(id, name, density, type, active_surface = active_surface)
        self.active_material_type = active_material_type
        self.formations = formations
        self.formations_history = formations_history
        self.composition = composition

    def get_resistance(self) -> object:
        # TODO see ActiveMaterial.DetermineResistance
        return None
    
    def get_history_material(self, history: str) -> 'ActiveMaterial':
        return ActiveMaterial(self.id, self.name, self.density, self.type, self.active_material_type, self.formations_history.get(history), self.formations_history, self.composition, self.active_surface)

@dataclass
class ActiveMaterialWeight:
    material: ActiveMaterial
    weight: float
    #material_date: Optional[str] 
