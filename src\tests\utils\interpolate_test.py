import math
import unittest
import pandas as pd

from utils.interpolate import interpolate

class TestInterpolate(unittest.TestCase):

    def test_interpolate_monotonic_increasing(self):
        x = pd.Series([ 1,   3,   5,   7,   9 ])
        y = pd.Series([ 100, 300, 500, 700, 900 ])
        v = pd.Series([ 1, 2, 3, 4, 5, 6, 7, 8, 9 ])
        result = interpolate(v, x, y)
        self.assertEqual(len(result), 9)
        self.assertEqual(result[0], 100)
        self.assertEqual(result[1], 200)
        self.assertEqual(result[2], 300)
        self.assertEqual(result[3], 400)
        self.assertEqual(result[4], 500)
        self.assertEqual(result[5], 600)
        self.assertEqual(result[6], 700)
        self.assertEqual(result[7], 800)
        self.assertEqual(result[8], 900)

    def test_interpolate_monotonic_decreasing(self):
        x = pd.Series([ 9,   7,   5,   3,   1 ])
        y = pd.Series([ 100, 300, 500, 700, 900 ])
        v = pd.Series([ 1, 2, 3, 4, 5, 6, 7, 8, 9 ])
        result = interpolate(v, x, y)
        self.assertEqual(len(result), 9)
        self.assertEqual(result[0], 900)
        self.assertEqual(result[1], 800)
        self.assertEqual(result[2], 700)
        self.assertEqual(result[3], 600)
        self.assertEqual(result[4], 500)
        self.assertEqual(result[5], 400)
        self.assertEqual(result[6], 300)
        self.assertEqual(result[7], 200)
        self.assertEqual(result[8], 100)

    def test_interpolate_monotonic_increasing_extrapolate_default(self):
        x = pd.Series([ 3,   5,   7 ])
        y = pd.Series([ 300, 500, 700 ])
        v = pd.Series([ 1, 2, 3, 4, 5, 6, 7, 8, 9 ])
        result = interpolate(v, x, y)
        self.assertEqual(len(result), 9)
        self.assertEqual(result[0], 300)
        self.assertEqual(result[1], 300)
        self.assertEqual(result[2], 300)
        self.assertEqual(result[3], 400)
        self.assertEqual(result[4], 500)
        self.assertEqual(result[5], 600)
        self.assertEqual(result[6], 700)
        self.assertEqual(result[7], 700)
        self.assertEqual(result[8], 700)

    def test_interpolate_monotonic_increasing_extrapolate_nan(self):
        x = pd.Series([ 3,   5,   7 ])
        y = pd.Series([ 300, 500, 700 ])
        v = pd.Series([ 1, 2, 3, 4, 5, 6, 7, 8, 9 ])
        result = interpolate(v, x, y, default=float('nan'))
        self.assertEqual(len(result), 9)
        self.assertTrue(math.isnan(result[0]))
        self.assertTrue(math.isnan(result[1]))
        self.assertEqual(result[2], 300)
        self.assertEqual(result[3], 400)
        self.assertEqual(result[4], 500)
        self.assertEqual(result[5], 600)
        self.assertEqual(result[6], 700)
        self.assertTrue(math.isnan(result[7]))
        self.assertTrue(math.isnan(result[8]))

    def test_interpolate_monotonic_increasing_extrapolate_value(self):
        x = pd.Series([ 3,   5,   7 ])
        y = pd.Series([ 300, 500, 700 ])
        v = pd.Series([ 1, 2, 3, 4, 5, 6, 7, 8, 9 ])
        result = interpolate(v, x, y, default=42)
        self.assertEqual(len(result), 9)
        self.assertEqual(result[0], 42)
        self.assertEqual(result[1], 42)
        self.assertEqual(result[2], 300)
        self.assertEqual(result[3], 400)
        self.assertEqual(result[4], 500)
        self.assertEqual(result[5], 600)
        self.assertEqual(result[6], 700)
        self.assertEqual(result[7], 42)
        self.assertEqual(result[8], 42)

if __name__ == '__main__':
    unittest.main()
