from typing import Any
from bson import ObjectId
from flask import Blueprint, request
import jsons
from pymongo.results import InsertOneResult
import datetime
from auth import bearer
from database import client, database
from db_actions import update_user_table, delete_optimization
from models.cell_design_model import CellDesignModel, CellDesignModelWithId, CellDesignModelWithMetadata
from settingslocal import VERSION
from utils.camelcase_to_snakecase import camelcase_to_snakecase, snakecase_to_camelcase
from jsons_config import jsons_flask_fork, jsons_mongodb_fork

cell_design_blueprint = Blueprint('cell_design_api', __name__, url_prefix='/api/cell-design')

def _list_to_filter(value: list) -> list:
    items = [
        _list_to_filter(item) if isinstance(item, list) else (_document_to_filter(item) if isinstance(item, dict) else item)
        for item in value
    ]

    return { '$all': items, '$size': len(value) }

def _expand_property_to_filter(key: str, value: Any) -> list[(str, Any)]:
    if isinstance(value, dict):
        return [
            (expanded_key, expanded_value)
            for (inner_key, inner_value) in value.items() for (expanded_key, expanded_value) in _expand_property_to_filter(f'{key}.{inner_key}', inner_value)
        ]
    if isinstance(value, list):
        return [(key, _list_to_filter(value))]
    else:
        return [(key, value)]

def _document_to_filter(document: dict[str, Any]) -> dict[str, Any]:
    return {
        expanded_key: expanded_value
        for (key, value) in document.items() for (expanded_key, expanded_value) in _expand_property_to_filter(key, value)
    }

_pipeline = [
    {
        '$lookup': {
            'from': 'users',
            'localField': 'createdBy',
            'foreignField': '_id',
            'as': 'createdBy'
        }
    }, {
        '$unwind': {
            'path': '$createdBy',
            'preserveNullAndEmptyArrays': True
        }
    }, {
        '$lookup': {
            'from': 'users',
            'localField': 'modifiedBy',
            'foreignField': '_id',
            'as': 'modifiedBy'
        }
    }, {
        '$unwind': {
            'path': '$modifiedBy',
            'preserveNullAndEmptyArrays': True
        }
    }, {
        '$lookup': {
            'from': 'users',
            'localField': 'deletedBy',
            'foreignField': '_id',
            'as': 'deletedBy'
        }
    }, {
        '$unwind': {
            'path': '$deletedBy',
            'preserveNullAndEmptyArrays': True
        }
    }, {
        '$lookup': {
            'from': 'users',
            'localField': 'releasedBy',
            'foreignField': '_id',
            'as': 'releasedBy'
        }
    }, {
        '$unwind': {
            'path': '$releasedBy',
            'preserveNullAndEmptyArrays': True
        }
    }
]

def _model_to_db_document(model: dict, type: type) -> dict:
    object = jsons.load(model, type, strip = True, key_transformer = camelcase_to_snakecase, transform_dict_keys = False)
    return jsons.dump(object, type, strict = True, key_transformer = snakecase_to_camelcase, transform_dict_keys = False)

def _db_document_to_model(document: dict, type: type) -> dict:
    # object = jsons.load(stringify_optional_dates(document), type, key_transformer = camelcase_to_snakecase, transform_dict_keys = False)
    object = jsons.load(document, type, strip = True, key_transformer = camelcase_to_snakecase, transform_dict_keys = False)
    return jsons.dump(object, type, strict = True, key_transformer = snakecase_to_camelcase, transform_dict_keys = False)


def get_design(cell_design_id: ObjectId) -> dict:
    with database['cell-designs'].aggregate([{ '$match': { '_id': cell_design_id } }] + _pipeline) as cursor:
        model = jsons.load(stringify_optional_fields(next(cursor, None)), CellDesignModelWithId, fork_inst = jsons_flask_fork, key_transformer = camelcase_to_snakecase, transform_dict_keys = False)

    return jsons.dump(model, CellDesignModelWithId, fork_inst = jsons_flask_fork, strict = True, key_transformer = snakecase_to_camelcase, transform_dict_keys = False)

def write_design(cell_design: CellDesignModelWithMetadata) -> InsertOneResult:
    document = jsons.dump(cell_design, CellDesignModelWithMetadata, fork_inst = jsons_mongodb_fork, key_transformer = snakecase_to_camelcase, transform_dict_keys = False)
    user = update_user_table(bearer.claims)
    at = datetime.datetime.now(tz=datetime.timezone.utc)
    year = at.isocalendar().year
    week = at.isocalendar().week
    with client.start_session() as session:
        # with session.start_transaction(read_concern = ReadConcern("local"), write_concern = WriteConcern("majority", wtimeout=1000), read_preference = ReadPreference.PRIMARY):
            cursor = database['cell-designs'].aggregate([
                {
                    '$addFields': {
                        'createdAtYear': { '$isoWeekYear': '$createdAt' }, 
                        'createdAtWeek': { '$isoWeek': '$createdAt' }
                    }
                }, {
                    '$match': {
                        'createdAtYear': year, 
                        'createdAtWeek': week
                    }
                }, {
                    '$count': 'num'
                }
            ], session = session)

            num = next(cursor, { 'num': 0 })['num']
            document = document | {
                'version': VERSION,
                'designId': f'CF_COM{(year - 2000):02}{week:02}{(num + 1):05}',
                'createdAt': at,
                'createdBy': user._id,
                'modifiedAt': at,
                'modifiedBy': user._id,
                'deletedAt': None,
                'deletedBy': None,
                'releasedAt': at if cell_design.released else None,
                'releasedBy': user._id if cell_design.released else None
            }

            # insert into database
            return database['cell-designs'].insert_one(document, session = session)

@cell_design_blueprint.get('/')
@bearer.secure('user')
def get_all_cell_designs() -> list[CellDesignModelWithId]:
    with database['cell-designs'].aggregate(_pipeline) as cursor:
        documents = [jsons.load(stringify_optional_fields(document), CellDesignModelWithId, fork_inst = jsons_flask_fork, key_transformer = camelcase_to_snakecase, transform_dict_keys = False, strict = False) for document in cursor]
        return [jsons.dump(document, CellDesignModelWithId, fork_inst = jsons_flask_fork, strict = True, key_transformer = snakecase_to_camelcase, transform_dict_keys = False) for document in documents if document.deleted_at is None]

@cell_design_blueprint.get('/<string:cell_design_id>')
@bearer.secure('user')
def get_single_cell_design(cell_design_id: str) -> CellDesignModelWithId:
    return get_design(ObjectId(cell_design_id))

@cell_design_blueprint.post('/')
@bearer.secure('user')
def create_cell_design() -> CellDesignModelWithId:
    body = request.get_json()

    filter_document: dict = _model_to_db_document(body, CellDesignModel)
    existing_document = database['cell-designs'].find_one(filter_document)

    if existing_document is not None:
        # a similar design already exists, notify the frontend with a 303 and abort.
        return get_design(existing_document['_id']), 303
    else:
        # get only properties in CellDesignModel
        cell_design = jsons.load(body, CellDesignModelWithMetadata, fork_inst = jsons_mongodb_fork, strip = True, key_transformer = camelcase_to_snakecase)
        if cell_design.released and not bearer.check_roles('release'):
            return 'NOT ALLOWED', 405

        result = write_design(cell_design)

        return get_design(result.inserted_id), 200

@cell_design_blueprint.put('/<string:cell_design_id>')
@bearer.secure('user')
def update_cell_design(cell_design_id: str) -> CellDesignModelWithId:
    body = request.get_json()

    # check if more than the metadata has changed
    filter_document: dict = _model_to_db_document(body, CellDesignModel)
    filter_document['_id'] = ObjectId(cell_design_id)
    filter_document = _document_to_filter(filter_document)
    existing_document = database['cell-designs'].find_one(filter_document)
    if existing_document is None:
        # the design values changed, notify the user with a 409 and abort.
        return 'CONFLICT', 409
    else:
        # get only properties in CellDesignModel
        cell_design = jsons.load(body, CellDesignModelWithMetadata, fork_inst = jsons_mongodb_fork, strip = True, key_transformer = camelcase_to_snakecase)
        existing_cell_design = jsons.load(existing_document, CellDesignModelWithMetadata, fork_inst = jsons_mongodb_fork, strip = True, key_transformer = camelcase_to_snakecase)
        
        # check whether the released state was changed
        released = None
        if cell_design.released != existing_cell_design.released:
            if not bearer.check_roles('release'):
                return 'NOT ALLOWED', 405
            released = cell_design.released

        document = jsons.dump(cell_design, CellDesignModelWithMetadata, fork_inst = jsons_mongodb_fork, key_transformer = snakecase_to_camelcase, transform_dict_keys = False)

        # attach metadata
        user = update_user_table(bearer.claims)
        at = datetime.datetime.now(tz=datetime.timezone.utc)
        document = document | {
            'modifiedAt': at,
            'modifiedBy': user._id,
            'deletedAt': None,
            'deletedBy': None
        }

        if released is not None:
            # release state changed, set metadata accordingly
            document = document | {
                'releasedAt': at if released else None,
                'releasedBy': user._id if released else None
            }

        database['cell-designs'].find_one_and_update({ '_id': ObjectId(cell_design_id) }, { '$set': document })
        return get_design(ObjectId(cell_design_id)), 200

@cell_design_blueprint.delete('/<string:cell_design_id>')
@bearer.secure('admin')
def delete_cell_design(cell_design_id: str) -> CellDesignModelWithId:
    # metadata to mark as deleted
    user = update_user_table(bearer.claims)
    at = datetime.datetime.now(tz=datetime.timezone.utc)
    metadata = {
        'deletedAt': at,
        'deletedBy': user._id
    }

    # update in database
    database['cell-designs'].find_one_and_update({ '_id': ObjectId(cell_design_id) }, { '$set': metadata })

    # update adjacent optimization
    design = get_design(ObjectId(cell_design_id))
    if design.get('optimizationId'):
        delete_optimization(ObjectId(design['optimizationId']), user)

    return design

# jsons throws a DeserializationError whenever a non-primitive type is deserialized to an 'Optional[type]' type (but works with if not Optional)
# Need to hack it to ensure 'Optional[type]' properties are passed as a string...
def stringify_optional_fields(document: dict) -> dict:
    if isinstance(document.get('releasedAt'), datetime.datetime):
        document['releasedAt'] = document['releasedAt'].isoformat(sep='T')

    if isinstance(document.get('deletedAt'), datetime.datetime):
        document['deletedAt'] = document['deletedAt'].isoformat(sep='T')

    if isinstance(document.get('optimizationId'), ObjectId):
        document['optimizationId'] = str(document['optimizationId'])

    return document
