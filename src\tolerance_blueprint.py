from auth import bearer
from flask import Blueprint, request
import jsons
from jsons_config import jsons_flask_fork

from utils.camelcase_to_snakecase import snakecase_to_camelcase, camelcase_to_snakecase
from models.cell_design_model import CellDesignModel, ToleranceCellDesign
from models.tolerance_result_model import ToleranceResponseModel
from electrochemistry.tolerance import Tolerance

tolerance_blueprint = Blueprint('tolerance_api', __name__, url_prefix='/api/tolerance')

@tolerance_blueprint.post('/')
@bearer.secure('user')
def tolerance():
    body = request.get_json()
    cell_design:ToleranceCellDesign = jsons.load(body, ToleranceCellDesign, key_transformer=camelcase_to_snakecase)
    tolerance = Tolerance(cell_design)
    result = tolerance.get_tolerance_response()

    return jsons.dump(result, ToleranceResponseModel, fork_inst = jsons_flask_fork, key_transformer = snakecase_to_camelcase)

