from collections.abc import Iterable
from contextvars import ContextVar
from functools import wraps
from typing import Any, Dict, Optional, Union

import jwt
import requests
from flask import request
from werkzeug.exceptions import Forbidden, Unauthorized


class OpenIdJwtDecoder:
    _jwks_uri: str
    _audience: Optional[str]
    _issuer: Optional[str]

    _jwks_client: jwt.PyJWKClient

    _algorithms: list[str]

    def __init__(self, endpoint: str, audience: Optional[str]):
        config: dict = requests.get(f"{endpoint}/.well-known/openid-configuration?appid={audience}").json()

        self._jwks_uri = config["jwks_uri"]
        self._audience = audience
        self._issuer = config.get("issuer", None)

        self._algorithms = config["id_token_signing_alg_values_supported"]
        self._jwks_client = jwt.PyJWKClient(self._jwks_uri)

    def decode(self, access_token) -> Dict[str, Any]:
        signing_key = self._jwks_client.get_signing_key_from_jwt(access_token)
        data = jwt.decode(
            access_token,
            key = signing_key.key,
            algorithms = self._algorithms,
            audience = self._audience,
        )

        return data

class OpenIdConnectBearer:
    _decoder: OpenIdJwtDecoder
    _claims_var: ContextVar[Dict[str, Any]]

    _mock_claims: Optional[Dict[str, Any]]

    def __init__(self, endpoint: str, audience: str, mock_claims: Optional[Dict[str, Any]] = None):
        self._decoder = OpenIdJwtDecoder(endpoint, audience)
        self._claims_var = ContextVar[Dict[str, Any]]("claims")
        self._mock_claims = mock_claims

    @property
    def claims(self) -> Dict[str, Any]:
        return self._claims_var.get()

    def secure(self, *conditions: Union[str, Iterable[str]]):
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                if self._mock_claims is None:
                    auth_header = request.headers.get("Authorization")
                    if auth_header is None:
                        raise Unauthorized("no authorization header found")
                    if not auth_header.startswith("Bearer "):
                        raise Unauthorized("authorization header has no bearer token")

                    token = auth_header[len("Bearer "):]
                    claims = self._decoder.decode(token)
                else:
                    # mock claims for testing
                    claims = self._mock_claims

                claims_roles = set(claims['roles'])
                if not OpenIdConnectBearer._check_roles(claims_roles, conditions):
                    raise Forbidden('the authenticated user is not having the required role')

                self._claims_var.set(claims)
                result = func(*args, **kwargs)
                self._claims_var.set(None)
                return result
            return wrapper
        return decorator
    
    def check_roles(self, *conditions: Union[str, Iterable[str]]) -> bool:
        claims_roles = set(self.claims['roles'])
        return OpenIdConnectBearer._check_roles(claims_roles, conditions)
        
    @staticmethod
    def _check_roles(claims_roles: set[str], conditions: tuple[Union[str, Iterable[str]], ...]) -> bool:
        return all(claims_roles.isdisjoint(roles) if roles is isinstance(roles, Iterable) else (roles in claims_roles) for roles in conditions)
