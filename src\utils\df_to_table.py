from typing import Optional
import pandas as pd

from models.table_model import TableModel, TableRowModel, TableCellModel

def df_to_table(df: pd.DataFrame, index_column_header: Optional[str] = None) -> TableModel:
    headers = [str(column) for column in df.columns]
    columns = [df[col] for col in df.columns]

    if index_column_header is not None:
        headers.insert(0, index_column_header)
        columns.insert(0, df.index)

    return TableModel(
        headers = headers,
        rows = [TableRowModel(cells = [TableCellModel(value = row[index], unit = None) for (index, _) in enumerate(headers)]) for row in zip(*columns)]
    )
