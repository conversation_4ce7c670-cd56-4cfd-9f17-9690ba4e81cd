from dataclasses import dataclass
import datetime
from typing import Optional, Union

from bson import ObjectId

from electrochemistry.serialization import CellFormatCylinderProperties, CellFormatPouchProperties, CellFormatPrismaProperties
from models.user_model import UserModel

@dataclass(kw_only = True)
class MaterialWeightModel:
    material_id: str
    weight_percent: float
    material_version_id: Optional[str] = None
    material_date: Optional[str] = None

@dataclass(kw_only = True)
class InactiveMaterialWeightModel:
    material_id: str
    weight_percent: float

@dataclass(kw_only = True)
class ToleranceMetricsVariableModel:
    id: str
    lower_limit: Optional[float] = None
    upper_limit: Optional[float] = None
    name:Optional[str] = None
    unit:Optional[str] = None

@dataclass(kw_only = True)
class TolearanceMetricsModel:
    variables: list[ToleranceMetricsVariableModel]
    value_type: Optional[str] = None

@dataclass(kw_only = True)
class CellDesignModel:
    # Cathode View
    cathode_materials: list[MaterialWeightModel]
    cathode_q_aim: Optional[float]
    cathode_q_aim_first_charge: Optional[float]
    cathode_fixed_u_range: Optional[list[float]] = None

    # Anode View
    anode_materials: list[MaterialWeightModel]
    anode_q_aim: Optional[float]
    anode_q_aim_first_charge: Optional[float]
    anode_q_aim_first_charge: Optional[float]
    anode_fixed_u_range: Optional[list[float]] = None
    prelithiation_capacity: Optional[float]

    # Balancing View
    balancing_u_min: float
    balancing_u_max: float
    balancing_np_ratio_first: float

    # Material View
    active_material_anode_weight_percent: float
    active_material_cathode_weight_percent: float

    anode_binder_materials: list[InactiveMaterialWeightModel]
    anode_conductive_additive_materials: list[InactiveMaterialWeightModel]

    cathode_binder_materials: list[InactiveMaterialWeightModel]
    cathode_conductive_additive_materials: list[InactiveMaterialWeightModel]

    prelithiation_process: str

    electrolyte_material_id: str
    separator_material_id: str
    aluminium_current_collector_material_id: str
    copper_current_collector_material_id: str

    # Electrode Pair View
    cathode_calander_density: float
    anode_calander_density: float

    cathode_areal_capacity: float

    thickness_aluminium_foil: float
    thickness_copper_foil: float
    thickness_separator: float

    # Cell View
    cell_format_id: str
    cell_format_is_default: bool
    cell_format_properties: Optional[Union[CellFormatCylinderProperties, CellFormatPrismaProperties, CellFormatPouchProperties]]
    electrolyte_swelling: float

    # Electrolyte View
    electrolyt_amount: float
    aging_table_edit_values: dict

    # Cell-Specs View
    safety: float
    parallel_cells_count: int
    serial_cells_count: int
    module_count: int

    # BOM View
    business_case_id: str
    scrap: float
    bom_prices: dict

    # Swelling
    swelling_during_formation: Optional[float]
    compressibility_over_pressure_range: Optional[float]

    # Tolerance
    tolerance_settings:Optional[TolearanceMetricsModel] = None


@dataclass
class ToleranceCellDesign(CellDesignModel):
    anode_loading: Optional[float] = None
    cathode_loading: Optional[float] = None
    np_ratio_rev: Optional[float] = None
    anode_coating: Optional[float] = None
    cathode_coating: Optional[float] = None

@dataclass(kw_only = True)
class CellDesignOptimizedModel(CellDesignModel):
    optimization_id: Optional[ObjectId] = None

@dataclass(kw_only = True)
class CellDesignModelWithMetadata(CellDesignOptimizedModel):
    name: str
    released: bool
    project_name: Optional[str]
    project_state: Optional[str]
    part_number: Optional[str]
    description: Optional[str]

@dataclass(kw_only = True)
class CellDesignModelGenerated(CellDesignModelWithMetadata):
    version: str = '2.0.0'
    design_id: str
    released_at: Optional[datetime.datetime]
    released_by: Optional[UserModel]
    created_at: datetime.datetime
    created_by: UserModel
    modified_at: datetime.datetime
    modified_by: UserModel
    deleted_at: Optional[datetime.datetime]
    deleted_by: Optional[UserModel]

@dataclass(kw_only = True)
class CellDesignModelWithId(CellDesignModelGenerated):
    _id: ObjectId
