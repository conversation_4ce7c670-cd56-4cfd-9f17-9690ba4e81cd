# Launch Configurations

```
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Backend",
            "type": "python",
            "request": "launch",
            "module": "flask",
            "cwd": "${workspaceFolder}/src",
            "env": {
                "FLASK_APP": "app.py",
                "FLASK_DEBUG": "1",
                "OIDC_ENDPOINT": "https://login.microsoftonline.com/0f4b4464-5743-4f40-b3a6-fa3ca6b04f30/v2.0",
                "OIDC_AUDIENCE": "f0edd3d0-1258-4764-bfa1-3a2a151abf9e"
            },
            "args": [
                "run",
                "--no-debugger",
                "--no-reload"
            ],
            "jinja": true,
            "justMyCode": true
        },
        {
            "name": "Backend [no auth]",
            "type": "python",
            "request": "launch",
            "module": "flask",
            "cwd": "${workspaceFolder}/src",
            "env": {
                "FLASK_APP": "app.py",
                "FLASK_DEBUG": "1",
                "USE_MOCK_BEARER": "1",
                "OIDC_ENDPOINT": "https://login.microsoftonline.com/0f4b4464-5743-4f40-b3a6-fa3ca6b04f30/v2.0",
                "OIDC_AUDIENCE": "f0edd3d0-1258-4764-bfa1-3a2a151abf9e"
            },
            "args": [
                "run",
                "--no-debugger",
                "--no-reload"
            ],
            "jinja": true,
            "justMyCode": true
        }
    ]
}
```