
class SolidElectrolyteInterface:
    growth_per_charge: float
    growth_nm_per_charge: float

    def __init__(self, growth_per_charge: float, a_act_an: float):
        self.growth_per_charge = growth_per_charge
        self.growth_nm_per_charge = growth_per_charge / a_act_an * 1e3
    
    def calculate_volume(self, capacity: float) -> tuple[float, float]:
        volume = self.growth_per_charge * capacity
        length = self.growth_nm_per_charge * capacity
        return (volume, length)
