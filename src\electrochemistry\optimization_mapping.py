from models.optimization_settings_model import OptimizationSettingsModel, ObjectiveVariables, OptimizationSettingParameter, OptimizationSettingVariable

optimization_variables = {
    'anode_weight_0': { 'name': 'Anode Weight Ratio Group14 SCC [%]', 'unit': 'w%'},
    'cathode_weight_0': { 'name': 'Cathode Weight Ratio BASF_NCM90_PC_BT98B [%]', 'unit': 'w%'},
    'balancing_np_ratio_first': { 'name': 'N/P Ratio First', 'unit': None},
    'electrolyt_amount': { 'name': 'Electrolyte amount [ml/Ah]', 'unit': "ml/Ah"},
    'thickness_aluminium_foil': { 'name': 'Thickness aluminium foil [µm]', 'unit': "µm"},
    'thickness_copper_foil': { 'name': 'Thickness copper foil [µm]', 'unit': "µm"},
    'thickness_separator': { 'name': 'Thickness separator [µm]', 'unit': "µm"},
    'cathode_calander_density': { 'name': 'Cathode density', 'unit': 'g/cm³'},
    'anode_calander_density': { 'name': 'Anode density', 'unit': 'g/cm³'},
    'cathode_areal_capacity': { 'name': 'Charge density cathode', 'unit': 'mAh/cm²'},
}

optimization_objectives = {
    'cost': { 'name': 'Cost [€/Cell]', 'unit': '€/Cell', "type":"min"},
    'capacity': { 'name': 'Capacity [Ah]', 'unit': 'Ah', "type":"max"},
    'energy': { 'name': 'Energy Content [Wh]', 'unit': 'Wh', "type":"max"},
    'energy_density_vol': { 'name': 'Energy Density Volumetric [Wh/l]', 'unit': 'Wh/l', "type":"max"},
    'energy_density_grav': { 'name': 'Energy Density Gravimetric [Wh/kg]', 'unit': 'Wh/kg', "type":"max"},
    'weight': { 'name': 'Weight [g]', 'unit': 'g', "type":"min"},
    'cf3_breathing_with_compression':{'name': "CF3 Breathing with compression [%]", 'unit':"%", 'type': 'min'}
}

optimization_parameters = {
    'brute': {'name': 'Grid'},
    'u_nom': {'name': 'Nominal Voltage'},
    'n_gen': {'name': 'Number of Generations'},
    'time': {'name': 'Runtime'},
    'nsga2': {'name': 'NSGA2'},
    'pso': {'name': 'PSO'},
    'bayesian':{'name': "Bayesian"}
} | optimization_variables | optimization_objectives

# TODO: move to data/optimization_settings.json, load and map model
settings: OptimizationSettingsModel = [
    ObjectiveVariables(
        objective = OptimizationSettingParameter(id = 'cost', name = 'Cost [€/Cell]', unit="€/Cell"),
        variables = [
            OptimizationSettingVariable(id = 'anode_weight_0', name = 'Anode Weight Ratio Group14 SCC [%]', unit = 'w%'),
            OptimizationSettingVariable(id = 'cathode_weight_0', name = 'Cathode Weight Ratio BASF_NCM90_PC_BT98B [%]', unit = 'w%'),
            OptimizationSettingVariable(id = 'balancing_np_ratio_first', name = 'N/P Ratio First', unit = None),
            OptimizationSettingVariable(id = 'electrolyt_amount', name = 'Electrolyte amount [ml/Ah]', unit = "ml/Ah"),
            OptimizationSettingVariable(id = 'thickness_aluminium_foil', name = 'Thickness aluminium foil [µm]', unit = "µm"),
            OptimizationSettingVariable(id = 'thickness_copper_foil', name = 'Thickness copper foil [µm]', unit = "µm"),
            OptimizationSettingVariable(id = 'thickness_separator', name = 'Thickness separator [µm]', unit = "µm"),
        ],
        stop_criterions = [],
        algorithms = [
            OptimizationSettingParameter(id = 'brute', name = 'Grid'),
            OptimizationSettingParameter(id = 'bayesian', name = 'Bayesian'),
        ],
        optimization_target=OptimizationSettingParameter(id = 'min', name = 'Minimize cost [€/Cell]')
    ),
    ObjectiveVariables(
        objective = OptimizationSettingParameter(id = 'capacity', name = 'Capacity [Ah]', unit="Ah"),
        variables = [
            OptimizationSettingVariable(id = 'anode_weight_0', name = 'Anode Weight Ratio Group14 SCC [%]', unit = 'w%'),
            OptimizationSettingVariable(id = 'cathode_weight_0', name = 'Cathode Weight Ratio BASF_NCM90_PC_BT98B [%]', unit = 'w%'),
            OptimizationSettingVariable(id = 'balancing_np_ratio_first', name = 'N/P Ratio First', unit = None),
            OptimizationSettingVariable(id = 'thickness_aluminium_foil', name = 'Thickness aluminium foil [µm]', unit = "µm"),
            OptimizationSettingVariable(id = 'thickness_copper_foil', name = 'Thickness copper foil [µm]', unit = "µm"),
            OptimizationSettingVariable(id = 'thickness_separator', name = 'Thickness separator [µm]', unit = "µm"),
        ],
        stop_criterions = [],
        algorithms = [
            OptimizationSettingParameter(id = 'brute', name = 'Grid'),
            OptimizationSettingParameter(id = 'bayesian', name = 'Bayesian'),
        ],
        optimization_target = OptimizationSettingParameter(id = 'max', name = 'Maximize capacity [Ah]')
    ),
    ObjectiveVariables(
        objective = OptimizationSettingParameter(id = 'energy', name = 'Energy Content [Wh]', unit="Wh"),
        variables = [
            OptimizationSettingVariable(id = 'anode_weight_0', name = 'Anode Weight Ratio Group14 SCC [%]', unit = 'w%'),
            OptimizationSettingVariable(id = 'cathode_weight_0', name = 'Cathode Weight Ratio BASF_NCM90_PC_BT98B [%]', unit = 'w%'),
            OptimizationSettingVariable(id = 'balancing_np_ratio_first', name = 'N/P Ratio First', unit = None),
            OptimizationSettingVariable(id = 'thickness_aluminium_foil', name = 'Thickness aluminium foil [µm]', unit = "µm"),
            OptimizationSettingVariable(id = 'thickness_copper_foil', name = 'Thickness copper foil [µm]', unit = "µm"),
            OptimizationSettingVariable(id = 'thickness_separator', name = 'Thickness separator [µm]', unit = "µm"),
        ],
        stop_criterions = [],
        algorithms = [
            OptimizationSettingParameter(id = 'brute', name = 'Grid'),
            OptimizationSettingParameter(id = 'bayesian', name = 'Bayesian'),
        ],
        optimization_target = OptimizationSettingParameter(id = 'max', name = 'Maximize energy content [Wh]')
    ),
    ObjectiveVariables(
        objective = OptimizationSettingParameter(id = 'energy_density_vol', name = 'Energy Density Volumetric [Wh/l]', unit="Wh/l"),
        variables = [
            OptimizationSettingVariable(id = 'anode_weight_0', name = 'Anode Weight Ratio Group14 SCC [%]', unit = 'w%'),
            OptimizationSettingVariable(id = 'cathode_weight_0', name = 'Cathode Weight Ratio BASF_NCM90_PC_BT98B [%]', unit = 'w%'),
            OptimizationSettingVariable(id = 'balancing_np_ratio_first', name = 'N/P Ratio First', unit = None),
            OptimizationSettingVariable(id = 'thickness_aluminium_foil', name = 'Thickness aluminium foil [µm]', unit = "µm"),
            OptimizationSettingVariable(id = 'thickness_copper_foil', name = 'Thickness copper foil [µm]', unit = "µm"),
            OptimizationSettingVariable(id = 'thickness_separator', name = 'Thickness separator [µm]', unit = "µm"),
        ],
        stop_criterions = [],
        algorithms = [
            OptimizationSettingParameter(id = 'brute', name = 'Grid'),
            OptimizationSettingParameter(id = 'bayesian', name = 'Bayesian'),
        ],
        optimization_target = OptimizationSettingParameter(id = 'max', name = 'Maximize energy density volumetric [Wh/l]')
    ),
    ObjectiveVariables(
        objective = OptimizationSettingParameter(id = 'energy_density_grav', name = 'Energy Density Gravimetric [Wh/kg]', unit="Wh/kg"),
        variables = [
            OptimizationSettingVariable(id = 'anode_weight_0', name = 'Anode Weight Ratio Group14 SCC [%]', unit = 'w%'),
            OptimizationSettingVariable(id = 'cathode_weight_0', name = 'Cathode Weight Ratio BASF_NCM90_PC_BT98B [%]', unit = 'w%'),
            OptimizationSettingVariable(id = 'balancing_np_ratio_first', name = 'N/P Ratio First', unit = None),
            OptimizationSettingVariable(id = 'electrolyt_amount', name = 'Electrolyte amount [ml/Ah]', unit = "ml/Ah"),
            OptimizationSettingVariable(id = 'thickness_aluminium_foil', name = 'Thickness aluminium foil [µm]', unit = "µm"),
            OptimizationSettingVariable(id = 'thickness_copper_foil', name = 'Thickness copper foil [µm]', unit = "µm"),
            OptimizationSettingVariable(id = 'thickness_separator', name = 'Thickness separator [µm]', unit = "µm"),
        ],
        stop_criterions = [],
        algorithms = [
            OptimizationSettingParameter(id = 'brute', name = 'Grid'),
            OptimizationSettingParameter(id = 'bayesian', name = 'Bayesian'),
        ],
        optimization_target = OptimizationSettingParameter(id = 'max', name = 'Maximize energy density gravimetric [Wh/kg]')
    ),
    ObjectiveVariables(
        objective = OptimizationSettingParameter(id = 'weight', name = 'Weight [g]', unit="g"),
        variables = [
            OptimizationSettingVariable(id = 'anode_weight_0', name = 'Anode Weight Ratio Group14 SCC [%]', unit = 'w%'),
            OptimizationSettingVariable(id = 'cathode_weight_0', name = 'Cathode Weight Ratio BASF_NCM90_PC_BT98B [%]', unit = 'w%'),
            OptimizationSettingVariable(id = 'balancing_np_ratio_first', name = 'N/P Ratio First', unit = None),
            OptimizationSettingVariable(id = 'electrolyt_amount', name = 'Electrolyte amount [ml/Ah]', unit = "ml/Ah"),
            OptimizationSettingVariable(id = 'thickness_aluminium_foil', name = 'Thickness aluminium foil [µm]', unit = "µm"),
            OptimizationSettingVariable(id = 'thickness_copper_foil', name = 'Thickness copper foil [µm]', unit = "µm"),
            OptimizationSettingVariable(id = 'thickness_separator', name = 'Thickness separator [µm]', unit = "µm"),
        ],
        stop_criterions = [],
        algorithms = [
            OptimizationSettingParameter(id = 'brute', name = 'Grid'),
            OptimizationSettingParameter(id = 'bayesian', name = 'Bayesian'),
        ],
        optimization_target=OptimizationSettingParameter(id = 'min', name = 'Minimize weight [g]')
    ),
    ObjectiveVariables(
        objective = OptimizationSettingParameter(id = 'cf3_breathing_with_compression', name = 'CF3 Breathing with compression [%]', unit="%"),
        variables = [
            OptimizationSettingVariable(id = 'anode_weight_0', name = 'Anode Weight Ratio Group14 SCC [%]', unit = 'w%'),
            OptimizationSettingVariable(id = 'cathode_weight_0', name = 'Cathode Weight Ratio BASF_NCM90_PC_BT98B [%]', unit = 'w%'),
            OptimizationSettingVariable(id = 'balancing_np_ratio_first', name = 'N/P Ratio First', unit = None),
            OptimizationSettingVariable(id = 'electrolyt_amount', name = 'Electrolyte amount [ml/Ah]', unit = "ml/Ah"),
            OptimizationSettingVariable(id = 'thickness_aluminium_foil', name = 'Thickness aluminium foil [µm]', unit = "µm"),
            OptimizationSettingVariable(id = 'thickness_copper_foil', name = 'Thickness copper foil [µm]', unit = "µm"),
            OptimizationSettingVariable(id = 'thickness_separator', name = 'Thickness separator [µm]', unit = "µm"),
        ],
        stop_criterions = [],
        algorithms = [
            OptimizationSettingParameter(id = 'brute', name = 'Grid'),
            OptimizationSettingParameter(id = 'bayesian', name = 'Bayesian'),
        ],
        optimization_target=OptimizationSettingParameter(id = 'min', name = 'Minimize swelling [%]')
    ),
    # ObjectiveVariables(
    #     objective = OptimizationSettingParameter(id = 'u_nom', name = 'Nominal Voltage'),
    #     variables = [
    #         OptimizationSettingVariable(id = 'anode_weight_0', name = 'Anode Weight', unit = 'w%'),
    #         OptimizationSettingVariable(id = 'balancing_np_ratio_first', name = 'N/P Ratio First'),
    #         OptimizationSettingVariable(id = 'cathode_calander_density', name = 'Cathode density', unit = 'g/cm³'),
    #         OptimizationSettingVariable(id = 'anode_calander_density', name = 'Anode density', unit = 'g/cm³'),
    #         OptimizationSettingVariable(id = 'cathode_areal_capacity', name = 'Charge density cathode', unit = 'mAh/cm²'),
    #     ],
    #     stop_criterions = [
    #         OptimizationSettingParameter(id = 'n_gen', name = 'Number of Generations'),
    #         OptimizationSettingParameter(id = 'time', name = 'Runtime'),
    #     ],
    #     algorithms = [
    #         OptimizationSettingParameter(id = 'nsga2', name = 'NSGA2'),
    #         OptimizationSettingParameter(id = 'pso', name = 'PSO'),
    #     ],
    # ),
]

# TODO: classify
