from dataclasses import dataclass
from typing import Optional
from electrochemistry.material import Material, MaterialType

@dataclass
class Electrolyte:

    material: Material

    swelling: float
    amount: float
    volume: float
    weight: float

    def __init__(self, material: Material, swelling: Optional[float] = None, amount: Optional[float] = None, capacity: Optional[float] = None):
        self.material = material
        self.swelling = swelling
        self.amount = amount
        if amount is not None and capacity is not None:
            self.volume = capacity * amount
            self.weight = self.volume * self.material.density
        else:
            self.volume = None
            self.weight = None
